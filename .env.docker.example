# Docker Environment Configuration for CV Duplicate Checker
# Copy this file to .env and fill in your values

# ========================================
# REQUIRED: OpenAI Configuration
# ========================================
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# ========================================
# Optional: OpenAI Settings
# ========================================
# OpenAI model to use (default: gpt-4o-mini)
OPENAI_MODEL=gpt-4o-mini

# Temperature for LLM responses (0 = deterministic, 1 = creative)
OPENAI_TEMPERATURE=0

# ========================================
# Optional: API Server Configuration
# ========================================
# Port to expose the API on your host machine
API_PORT=8000

# Log level for the application
API_LOG_LEVEL=info

# Enable auto-reload for development (true/false)
API_RELOAD=false

# Enable debug mode (true/false)
DEBUG=false

# ========================================
# Redis Caching Configuration
# ========================================
# Enable Redis caching for LLM semantic similarity results
REDIS_ENABLED=true

# Redis connection settings (use 'redis' for Docker Compose service name)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis cache settings
REDIS_TTL=3600
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_CONNECT_TIMEOUT=5
REDIS_SOCKET_KEEPALIVE=true
REDIS_RETRY_ON_TIMEOUT=true
REDIS_HEALTH_CHECK_INTERVAL=30
REDIS_KEY_PREFIX=cv-dup-checker

# ========================================
# Optional: Observability & Monitoring
# ========================================
# Langfuse Configuration (LLM observability)
LANGFUSE_SECRET_KEY=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_HOST="https://langfuse.xstaging.navigosgroup.site"

# Sentry Configuration (error monitoring)
SENTRY_DSN=
SENTRY_ENABLED=false
SENTRY_ENVIRONMENT=production
SENTRY_SAMPLE_RATE=1.0
SENTRY_TRACES_SAMPLE_RATE=0.1

# ========================================
# Docker Usage Examples
# ========================================
#
# Production (includes Redis):
# docker-compose up -d
#
# Development with live reload:
# docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
#
# Build and run:
# docker-compose up --build
#
# View logs:
# docker-compose logs -f cv-duplicate-checker
# docker-compose logs -f redis
#
# Check Redis status:
# docker-compose exec redis redis-cli ping
#
# Monitor Redis cache:
# docker-compose exec redis redis-cli monitor
#
# Stop services:
# docker-compose down
#
# Stop and remove volumes:
# docker-compose down -v
#
# ========================================
# Redis Cache Benefits
# ========================================
# - Persistent caching across application restarts
# - Shared cache across multiple application instances  
# - Significant performance improvement (up to 68x faster)
# - Reduced OpenAI API costs through cache hits
# - Graceful fallback to memory cache if Redis unavailable
#
# ========================================

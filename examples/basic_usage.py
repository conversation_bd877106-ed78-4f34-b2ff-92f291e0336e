"""Basic usage example for CV Duplicate Checker Agent"""

import asyncio
import json
import sys
import os

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from cv_duplicate_checker import CVDuplicateCheckerAgent


async def main():
    """Example usage of the CV Duplicate Checker Agent with parallel workflow"""

    # Initialize agent
    agent = CVDuplicateCheckerAgent()

    # Existing CV data
    existing_cv = {
        "education": [
            {
                "school": "Đại học Bách Khoa",
                "major": "<PERSON>hoa học máy tính",
                "start_date": "2018",
                "end_date": "2022",
            }
        ],
        "experience": [
            {
                "title": "Software Engineer",
                "company": "Tech Corp",
                "start_date": "2022-01-01",
                "description": "Phát triển ứng dụng web",
            }
        ],
        "certification": [
            {"certification_name": "AWS Developer", "organization": "Amazon"}
        ],
        "language": [{"language_name": "English"}],
        "extra_curricular": [],
        "achievement": [],
        "reference": [],
        "external_doc": [],
        "summary": [],
        "hobby": [],
    }

    # New data to check
    new_data = {
        "education": [
            {
                "school": "Đại học Bách Khoa",
                "major": "Khoa học máy tính",
                "start_date": "2018",
                "end_date": "2022",
                "degree": "Cử nhân",  # New information
                "gpa": 3.5,  # New information
            }
        ],
        "experience": [
            {
                "title": "Software Engineer",
                "company": "Tech Corp",
                "start_date": "2022-01-01",
                "description": "Phát triển ứng dụng web",
                "end_date": "2023-12-31",  # New information
                "skills": ["Python", "JavaScript"],  # New information
            },
            {
                "title": "Senior Developer",  # Completely new
                "company": "New Company",
                "start_date": "2024-01-01",
                "description": "Lead development team",
            },
        ],
        "certification": [
            {
                "certification_name": "AWS Developer",
                "organization": "Amazon",
                "issue_date": "2023-01-01",  # New information
                "expire_date": "2026-01-01",  # New information
            }
        ],
        "language": [
            {
                "language_name": "English",
                "level": "Advanced",  # New information
            },
            {
                "language_name": "Japanese",  # Completely new
                "level": "Intermediate",
            },
        ],
        "extra_curricular": [],
        "achievement": [],
        "reference": [],
        "external_doc": [],
        "summary": [],
        "hobby": [],
    }

    # Process
    result_dict = await agent.process(
        json.dumps(existing_cv, ensure_ascii=False),
        json.dumps(new_data, ensure_ascii=False),
    )

    # Check if error occurred
    if "error" in result_dict:
        print("Error occurred:", result_dict["error"])
        return

    print("Action Summary:", result_dict["action_summary"])
    print("Section Summary:", result_dict["section_summary"])
    print("\nFull Result:")
    print(json.dumps(result_dict, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    asyncio.run(main())

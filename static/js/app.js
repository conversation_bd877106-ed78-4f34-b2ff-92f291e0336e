/**
 * CV Duplicate Checker Web Interface JavaScript
 */

// Global variables
let validationTimeouts = {};

/**
 * Load sample data into the form
 */
function loadSampleData(type) {
    const sampleData = JSON.parse(document.getElementById('sample' + capitalizeFirst(type)).textContent);

    // Format and load the data
    document.getElementById('existingCvData').value = JSON.stringify(sampleData.existing_cv, null, 2);
    document.getElementById('newCvData').value = JSON.stringify(sampleData.new_data, null, 2);

    // Validate both inputs
    validateJsonInput('existingCvData');
    validateJsonInput('newCvData');

    // Save to localStorage
    saveToLocalStorage();

    // Show success message
    showNotification(`Loaded ${type} example data`, 'success');
}

/**
 * Clear the form
 */
function clearForm() {
    document.getElementById('existingCvData').value = '';
    document.getElementById('newCvData').value = '';

    // Clear validation indicators
    clearValidationIndicator('existingCvData');
    clearValidationIndicator('newCvData');

    // Clear localStorage
    localStorage.removeItem('cvTestForm');

    showNotification('Form cleared', 'info');
}

/**
 * Validate JSON input
 */
function validateJsonInput(inputId) {
    const input = document.getElementById(inputId);
    const indicator = document.getElementById(inputId.replace('Data', 'Indicator'));

    // Clear previous timeout
    if (validationTimeouts[inputId]) {
        clearTimeout(validationTimeouts[inputId]);
    }

    // Show validating state
    indicator.className = 'json-validation-indicator validating';

    // Debounce validation
    validationTimeouts[inputId] = setTimeout(() => {
        try {
            const value = input.value.trim();
            if (value === '') {
                clearValidationIndicator(inputId);
                return;
            }

            JSON.parse(value);

            // Valid JSON
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            indicator.className = 'json-validation-indicator valid';

        } catch (error) {
            // Invalid JSON
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            indicator.className = 'json-validation-indicator invalid';
            indicator.title = 'Invalid JSON: ' + error.message;
        }
    }, 500);
}

/**
 * Clear validation indicator
 */
function clearValidationIndicator(inputId) {
    const input = document.getElementById(inputId);
    const indicator = document.getElementById(inputId.replace('Data', 'Indicator'));

    input.classList.remove('is-valid', 'is-invalid');
    indicator.className = 'json-validation-indicator';
    indicator.title = '';
}

/**
 * Setup JSON validation for textareas
 */
function setupJsonValidation() {
    const jsonInputs = document.querySelectorAll('.json-input');

    jsonInputs.forEach(input => {
        // Prevent multiple event listeners on the same input
        if (input.hasAttribute('data-validation-setup')) {
            return;
        }
        input.setAttribute('data-validation-setup', 'true');

        input.addEventListener('input', function() {
            validateJsonInput(this.id);
            saveToLocalStorage();
        });

        input.addEventListener('blur', function() {
            formatJson(this.id);
        });
    });
}

/**
 * Format JSON in textarea
 */
function formatJson(inputId) {
    const input = document.getElementById(inputId);
    try {
        const value = input.value.trim();
        if (value !== '') {
            const parsed = JSON.parse(value);
            input.value = JSON.stringify(parsed, null, 2);
        }
    } catch (error) {
        // Don't format if invalid JSON
    }
}

/**
 * Setup form submission
 */
function setupFormSubmission() {
    const form = document.getElementById('cvTestForm');
    if (!form) return;

    // Prevent multiple event listeners on the same form
    if (form.hasAttribute('data-submission-setup')) {
        return;
    }
    form.setAttribute('data-submission-setup', 'true');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('submitBtn');
        const submitText = document.getElementById('submitText');
        const submitSpinner = document.getElementById('submitSpinner');

        // Validate inputs before submission
        const existingCvInput = document.getElementById('existingCvData');
        const newCvInput = document.getElementById('newCvData');

        try {
            JSON.parse(existingCvInput.value);
            JSON.parse(newCvInput.value);
        } catch (error) {
            showNotification('Please ensure both JSON inputs are valid before submitting', 'danger');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitText.textContent = 'Processing...';
        submitSpinner.classList.remove('d-none');

        // Create form data
        const formData = new FormData();
        formData.append('existing_cv', existingCvInput.value);
        formData.append('new_data', newCvInput.value);

        // Submit form
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                // Get the HTML response and replace the page content
                return response.text();
            } else {
                return response.text().then(text => {
                    throw new Error(`Server error: ${response.status} - ${text}`);
                });
            }
        })
        .then(html => {
            // Replace the current page content with the response
            document.open();
            document.write(html);
            document.close();
        })
        .catch(error => {
            console.error('Form submission error:', error);
            showNotification('Error submitting form: ' + error.message, 'danger');
        })
        .finally(() => {
            // Reset loading state
            submitBtn.disabled = false;
            submitText.textContent = 'Check for Duplicates';
            submitSpinner.classList.add('d-none');
        });
    });
}

/**
 * Save form data to localStorage
 */
function saveToLocalStorage() {
    const existingCv = document.getElementById('existingCvData')?.value || '';
    const newData = document.getElementById('newCvData')?.value || '';

    const formData = {
        existingCv: existingCv,
        newData: newData,
        timestamp: Date.now()
    };

    localStorage.setItem('cvTestForm', JSON.stringify(formData));
}

/**
 * Load saved data from localStorage
 */
function loadSavedData() {
    const savedData = localStorage.getItem('cvTestForm');
    if (!savedData) return;

    try {
        const formData = JSON.parse(savedData);

        // Only load if data is less than 24 hours old
        const twentyFourHours = 24 * 60 * 60 * 1000;
        if (Date.now() - formData.timestamp < twentyFourHours) {
            const existingCvInput = document.getElementById('existingCvData');
            const newDataInput = document.getElementById('newCvData');

            if (existingCvInput && formData.existingCv) {
                existingCvInput.value = formData.existingCv;
                validateJsonInput('existingCvData');
            }

            if (newDataInput && formData.newData) {
                newDataInput.value = formData.newData;
                validateJsonInput('newCvData');
            }
        } else {
            // Clear expired data
            localStorage.removeItem('cvTestForm');
        }
    } catch (error) {
        console.error('Error loading saved data:', error);
        localStorage.removeItem('cvTestForm');
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        return navigator.clipboard.writeText(text);
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            return Promise.resolve();
        } catch (err) {
            return Promise.reject(err);
        } finally {
            document.body.removeChild(textArea);
        }
    }
}

/**
 * Utility function to capitalize first letter
 */
function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Initialize page-specific functionality
 */
function initializePage() {
    // Check if we're on the main test page
    if (document.getElementById('cvTestForm')) {
        setupJsonValidation();
        setupFormSubmission();
        loadSavedData();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializePage);

/**
 * Handle keyboard shortcuts
 */
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + Enter to submit form
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        const form = document.getElementById('cvTestForm');
        if (form) {
            form.dispatchEvent(new Event('submit'));
        }
    }

    // Ctrl/Cmd + K to clear form
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        if (document.getElementById('cvTestForm')) {
            clearForm();
        }
    }
});

/**
 * Handle window beforeunload to save form data
 */
window.addEventListener('beforeunload', function() {
    if (document.getElementById('cvTestForm')) {
        saveToLocalStorage();
    }
});

/**
 * Export functions for global access
 */
window.CVDuplicateChecker = {
    loadSampleData,
    clearForm,
    validateJsonInput,
    formatJson,
    copyToClipboard,
    showNotification
};

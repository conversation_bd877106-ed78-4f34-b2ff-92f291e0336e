"""
FastAPI application for CV Duplicate Checker Agent
"""

import json
import logging
import os
from contextlib import asynccontextmanager

import sentry_sdk
from fastapi import FastAPI, Form, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import <PERSON><PERSON>2<PERSON>emplates

from .agent import CVDuplicateCheckerAgent
from .api_models import (
    ActionDetail,
    ActionSummary,
    CacheOperationResponse,
    CacheStatsResponse,
    CVCheckRequest,
    CVCheckResponse,
    CVData,
    ErrorResponse,
    HealthResponse,
    NormalizationInfo,
    SectionSummary,
)
from .cache import get_cache
from .config import MAX_REQUEST_SIZE, get_debug_mode

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Agent instance with lifecycle management
agent: CVDuplicateCheckerAgent = None

# Setup templates and static files
templates = Jinja2Templates(directory="templates")


async def cleanup_agent():
    """Clean up current agent instance to prevent memory leaks"""
    global agent
    if agent:
        # Clear any large objects the agent might be holding
        if hasattr(agent, "llm"):
            # Clear LLM client cache if exists
            del agent.llm
        if hasattr(agent, "graph"):
            # Clear graph state
            del agent.graph
        del agent
        agent = None
        # Force garbage collection
        import gc

        gc.collect()
        logger.info("Agent cleaned up successfully")


async def get_agent() -> CVDuplicateCheckerAgent:
    """Get agent instance, initializing if needed"""
    global agent

    # Initialize agent if needed
    if agent is None:
        try:
            agent = CVDuplicateCheckerAgent()
            logger.info("CV Duplicate Checker Agent initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize agent: {str(e)}")
            raise HTTPException(status_code=500, detail="Agent initialization failed")

    return agent


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting CV Duplicate Checker API...")
    try:
        await get_agent()  # This will initialize the agent
    except Exception as e:
        logger.error(f"Failed to initialize agent: {str(e)}")

    yield

    # Shutdown
    logger.info("Shutting down CV Duplicate Checker API...")
    await cleanup_agent()

    # Close cache connections
    try:
        from .cache import close_cache

        await close_cache()
        logger.info("Cache connections closed successfully")
    except Exception as e:
        logger.warning(f"Error closing cache connections: {e}")


# Create FastAPI application
app = FastAPI(
    title="CV Duplicate Checker API",
    description="""
    An intelligent CV duplicate detection and merging system powered by LangGraph and OpenAI's LLM for semantic understanding.

    ## Features

    - **Semantic Matching**: Uses LLM to understand that "DHBK" equals "Đại học Bách Khoa"
    - **Parallel Processing**: All CV sections processed simultaneously using LangGraph
    - **Multi-language Support**: Handles Vietnamese ↔ English translations seamlessly
    - **Smart Actions**: ADD new info, UPDATE missing fields, or SKIP complete duplicates
    - **Reliable**: Graceful fallbacks if LLM unavailable
    - **Comprehensive**: Handles 10 CV sections (education, experience, certifications, etc.)

    ## Usage

    1. **POST /check-duplicates**: Process CV data and detect duplicates
    2. **GET /health**: Check API health status
    3. **GET /docs**: View interactive API documentation
    """,
    version="1.0.0",
    contact={
        "name": "CV Duplicate Checker API",
        "url": "https://github.com/your-repo/upzi-cv-duplicate-checker",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan,
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")


# Add request size validation middleware
@app.middleware("http")
async def validate_request_size(request: Request, call_next):
    """Validate request size to prevent DoS attacks"""
    content_length = request.headers.get("content-length")
    if content_length and int(content_length) > MAX_REQUEST_SIZE:
        return JSONResponse(
            status_code=413,
            content={
                "error": "Request too large",
                "message": f"Request size exceeds {MAX_REQUEST_SIZE} bytes",
            },
        )
    return await call_next(request)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv(
        "ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8000"
    ).split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization"],
)


def parse_agent_result(result_json: dict) -> CVCheckResponse:
    """Parse agent result into API response model"""
    try:
        # Parse processed CV
        processed_cv = CVData(**result_json["processed_cv"])

        # Parse action summary
        action_summary = ActionSummary(**result_json["action_summary"])

        # Parse section summary
        section_summary = {}
        for section, summary in result_json["section_summary"].items():
            section_summary[section] = SectionSummary(**summary)

        # Parse detailed actions
        detailed_actions = []
        for action in result_json["detailed_actions"]:
            detailed_actions.append(ActionDetail(**action))

        # Parse normalization info if available
        normalization_info = None
        if "performance_metrics" in result_json:
            metrics = result_json["performance_metrics"]
            normalization_time = metrics.get("normalization_time", 0)
            normalization_stats = metrics.get("normalization_stats", {})

            normalization_info = NormalizationInfo(
                existing_cv_normalized=normalization_time > 0,
                new_data_normalized=normalization_time > 0,
                normalization_time=normalization_time,
                cached_normalizations=normalization_stats.get(
                    "cached_normalizations", 0
                ),
            )

        return CVCheckResponse(
            processed_cv=processed_cv,
            action_summary=action_summary,
            section_summary=section_summary,
            detailed_actions=detailed_actions,
            normalization_info=normalization_info,
        )
    except Exception as e:
        logger.error(f"Failed to parse agent result: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to parse processing result: {str(e)}",
        )


@app.get("/health", response_model=HealthResponse, tags=["Health"])
async def health_check():
    """
    Check the health status of the API, agent, Redis cache, and Langfuse tracing service
    """
    try:
        # Try to get agent to ensure it's available
        test_agent = await get_agent()
        agent_ready = test_agent is not None
    except Exception:
        agent_ready = False

    # Check Redis cache health
    try:
        cache = await get_cache()
        cache_info = await cache.get_cache_info()
        redis_healthy = cache_info.get("redis_healthy", False)
    except Exception:
        redis_healthy = False

    # Determine overall status
    status = "healthy" if agent_ready else "degraded"
    if not redis_healthy:
        status = "degraded" if status == "healthy" else "unhealthy"

    return HealthResponse(
        status=status,
        version="1.0.0",
        agent_ready=agent_ready,
        redis_healthy=redis_healthy,
    )


@app.post(
    "/check-duplicates",
    response_model=CVCheckResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid input data"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    tags=["CV Processing"],
)
async def check_cv_duplicates(request: CVCheckRequest):
    """
    Process CV data to detect and merge duplicates using semantic matching

    This endpoint:
    1. Compares existing CV data with new data
    2. Uses LLM-powered semantic matching to understand equivalences
    3. Returns merged CV with detailed action reports

    **Semantic Matching Examples:**
    - "DHBK" ↔ "Đại học Bách Khoa"
    - "Google" ↔ "Google LLC"
    - "MIT" ↔ "Massachusetts Institute of Technology"

    **Action Types:**
    - **ADD**: Completely new information
    - **UPDATE**: Existing entry with missing fields
    - **SKIP**: Complete duplicate found
    """
    try:
        # Get or refresh agent instance
        current_agent = await get_agent()

        # Convert request data to JSON strings for agent processing
        existing_cv_json = json.dumps(
            request.existing_cv.model_dump(), ensure_ascii=False
        )
        new_data_json = json.dumps(request.new_data.model_dump(), ensure_ascii=False)

        logger.info("Processing CV duplicate check request...")

        # Process with agent including tracing context
        result = await current_agent.process(existing_cv_json, new_data_json)

        # Parse and return response
        response = parse_agent_result(result)

        logger.info(
            f"CV processing completed. Actions: {response.action_summary.total_actions}"
        )
        return response

    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        # Add CV processing context to Sentry
        with sentry_sdk.configure_scope() as scope:
            scope.set_tag("error_type", "CVValidationError")
            scope.set_context(
                "cv_processing",
                {
                    "stage": "validation",
                    "existing_cv_sections": list(
                        request.existing_cv.model_dump().keys()
                    )
                    if request.existing_cv
                    else [],
                    "new_data_sections": list(request.new_data.model_dump().keys())
                    if request.new_data
                    else [],
                },
            )
            sentry_sdk.capture_exception(e)

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid input data: {str(e)}",
        )
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        # Add comprehensive CV processing context to Sentry
        with sentry_sdk.configure_scope() as scope:
            scope.set_tag("error_type", "CVProcessingError")
            scope.set_context(
                "cv_processing",
                {
                    "stage": "agent_processing",
                    "existing_cv_sections": list(
                        request.existing_cv.model_dump().keys()
                    )
                    if request.existing_cv
                    else [],
                    "new_data_sections": list(request.new_data.model_dump().keys())
                    if request.new_data
                    else [],
                    "agent_initialized": current_agent is not None,
                },
            )
            scope.set_extra("exception_type", type(e).__name__)
            sentry_sdk.capture_exception(e)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process CV data: {str(e)}",
        )


@app.get(
    "/cache/stats",
    response_model=CacheStatsResponse,
    tags=["Cache Management"],
)
async def get_cache_stats():
    """
    Get comprehensive cache statistics including Redis and memory cache performance
    """
    try:
        cache = await get_cache()
        cache_info = await cache.get_cache_info()

        return CacheStatsResponse(**cache_info)

    except Exception as e:
        logger.error(f"Failed to get cache stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve cache statistics: {str(e)}",
        )


@app.post(
    "/cache/clear",
    response_model=CacheOperationResponse,
    tags=["Cache Management"],
)
async def clear_cache():
    """
    Clear all cached data (Redis and memory caches)

    **Note**: This operation will clear all semantic similarity cache data.
    Use with caution in production environments.
    """
    try:
        cache = await get_cache()
        redis_cleared = await cache.clear_cache()

        return CacheOperationResponse(
            success=True,
            message="Cache cleared successfully",
            details={
                "redis_cleared": redis_cleared,
                "memory_cleared": True,
            },
        )

    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}",
        )


@app.get("/", tags=["Root"])
async def root():
    """
    API root endpoint with basic information
    """
    return {
        "message": "CV Duplicate Checker API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "web_interface": "/web",
        "endpoints": {
            "check_duplicates": "/check-duplicates",
            "health": "/health",
            "cache_stats": "/cache/stats",
            "cache_clear": "/cache/clear",
            "web_interface": "/web",
            "web_health": "/web/health",
        },
    }


# Web Interface Routes


@app.get(
    "/web",
    response_class=HTMLResponse,
    tags=["Web Interface"],
    operation_id="web_interface_get",
)
@app.post(
    "/web",
    response_class=HTMLResponse,
    tags=["Web Interface"],
    operation_id="web_interface_post",
)
async def web_interface(
    request: Request, existing_cv: str = Form(None), new_data: str = Form(None)
):
    """
    Main web interface for testing the CV duplicate checker
    Handles both form display (GET) and processing (POST)
    """
    # Handle GET request - show form
    if request.method == "GET":
        return templates.TemplateResponse("index.html", {"request": request})

    # Handle POST request - process form data
    if not existing_cv or not new_data:
        return templates.TemplateResponse(
            "index.html",
            {
                "request": request,
                "messages": [("error", "Both existing CV and new data are required")],
            },
        )

    try:
        # Get or refresh agent instance
        current_agent = await get_agent()

        # Validate JSON inputs
        json.loads(existing_cv)  # Validate existing_cv JSON
        json.loads(new_data)  # Validate new_data JSON

        result = await current_agent.process(existing_cv, new_data)

        # Parse result
        response = parse_agent_result(result)

        # Create enhanced actions with display names for template rendering
        enhanced_actions = []
        for action in response.detailed_actions:
            display_fields = [
                "school",
                "company",
                "title",
                "certification_name",
                "language_name",
                "name",
                "hobby",
                "role",
            ]
            display_name = ""
            for field in display_fields:
                if field in action.item and action.item[field]:
                    display_name = action.item[field]
                    break

            # Create a dict with all action data plus display_name
            enhanced_action = action.model_dump()
            enhanced_action["display_name"] = display_name
            enhanced_actions.append(enhanced_action)

        # Format processed CV for display
        processed_cv_json = json.dumps(
            response.processed_cv.model_dump(), indent=2, ensure_ascii=False
        )
        raw_response = json.dumps(
            {
                "processed_cv": response.processed_cv.model_dump(),
                "action_summary": response.action_summary.model_dump(),
                "section_summary": {
                    k: v.model_dump() for k, v in response.section_summary.items()
                },
                "detailed_actions": [
                    action.model_dump() for action in response.detailed_actions
                ],
            },
            indent=2,
            ensure_ascii=False,
        )

        return templates.TemplateResponse(
            "index.html",
            {
                "request": request,
                "result": response,
                "enhanced_actions": enhanced_actions,
                "processed_cv_json": processed_cv_json,
                "raw_response": raw_response,
                "existing_cv": existing_cv,
                "new_data": new_data,
                "show_results": True,
                "result_action_summary": response.action_summary.model_dump(),
                "result_section_summary": {
                    k: v.model_dump() for k, v in response.section_summary.items()
                },
                "result_processed_cv": response.processed_cv.model_dump(),
            },
        )

    except json.JSONDecodeError as e:
        return templates.TemplateResponse(
            "index.html",
            {
                "request": request,
                "messages": [("error", f"Invalid JSON format: {str(e)}")],
            },
        )
    except Exception as e:
        logger.error(f"Web processing error: {str(e)}")
        return templates.TemplateResponse(
            "index.html",
            {
                "request": request,
                "messages": [("error", f"Processing error: {str(e)}")],
            },
        )


@app.get("/web/health", response_class=HTMLResponse, tags=["Web Interface"])
async def web_health(request: Request):
    """
    Web interface health status page
    """
    try:
        # Get health data from API endpoint
        health_data = await health_check()

        return templates.TemplateResponse(
            "health.html", {"request": request, "health_data": health_data.model_dump()}
        )
    except Exception as e:
        logger.error(f"Web health check error: {str(e)}")
        return templates.TemplateResponse(
            "health.html",
            {
                "request": request,
                "health_data": {
                    "status": "error",
                    "version": "1.0.0",
                    "agent_ready": False,
                },
                "error": str(e),
            },
        )


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler with Sentry context"""
    # Add request context to Sentry
    with sentry_sdk.configure_scope() as scope:
        scope.set_tag("error_type", "HTTPException")
        scope.set_context(
            "request",
            {
                "url": str(request.url),
                "method": request.method,
                "headers": dict(request.headers),
            },
        )
        scope.set_extra("status_code", exc.status_code)
        scope.set_extra("detail", exc.detail)

        # Only capture non-4xx errors in Sentry (business logic errors)
        if exc.status_code >= 500:
            sentry_sdk.capture_exception(exc)

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTPException",
            "message": exc.detail,
            "status_code": exc.status_code,
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler with enhanced Sentry integration"""
    logger.error(f"Unhandled exception: {str(exc)}")

    # Enhanced Sentry context for unhandled exceptions
    with sentry_sdk.configure_scope() as scope:
        scope.set_tag("error_type", "UnhandledException")
        scope.set_context(
            "request",
            {
                "url": str(request.url),
                "method": request.method,
                "headers": dict(request.headers),
                "path_params": getattr(request, "path_params", {}),
            },
        )
        scope.set_extra("exception_type", type(exc).__name__)

        # Capture the exception in Sentry
        sentry_sdk.capture_exception(exc)

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "InternalServerError",
            "message": "An unexpected error occurred",
            "details": str(exc) if get_debug_mode() else None,
        },
    )


if __name__ == "__main__":
    import uvicorn

    # Load environment variables
    from dotenv import load_dotenv

    load_dotenv()

    # Run the API
    uvicorn.run(
        "cv_duplicate_checker.api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )

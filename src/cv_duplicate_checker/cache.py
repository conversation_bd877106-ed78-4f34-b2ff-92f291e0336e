"""Redis-based caching system for LLM semantic similarity results"""

import asyncio
import json
import logging
import time
from functools import lru_cache
from typing import Any, Dict, List, Optional, Tuple, Union

import redis.asyncio as aioredis
from redis.asyncio import Redis
from redis.exceptions import ConnectionError, RedisError, TimeoutError

from .config import (
    REDIS_DB,
    REDIS_ENABLED,
    REDIS_HEALTH_CHECK_INTERVAL,
    REDIS_HOST,
    REDIS_KEY_PREFIX,
    REDIS_MAX_CONNECTIONS,
    REDIS_PASSWORD,
    REDIS_PORT,
    REDIS_RETRY_ON_TIMEOUT,
    REDIS_SOCKET_CONNECT_TIMEOUT,
    REDIS_SOCKET_KEEPALIVE,
    REDIS_TTL,
    REDIS_URL,
    SEMANTIC_CACHE_SIZE,
)

logger = logging.getLogger(__name__)


class CacheStats:
    """Track cache performance statistics"""

    def __init__(self):
        self.redis_hits = 0
        self.redis_misses = 0
        self.memory_hits = 0
        self.memory_misses = 0
        self.redis_errors = 0
        self.total_operations = 0

    def redis_hit(self):
        self.redis_hits += 1
        self.total_operations += 1

    def redis_miss(self):
        self.redis_misses += 1
        self.total_operations += 1

    def memory_hit(self):
        self.memory_hits += 1
        self.total_operations += 1

    def memory_miss(self):
        self.memory_misses += 1
        self.total_operations += 1

    def redis_error(self):
        self.redis_errors += 1

    def get_stats(self) -> Dict[str, Union[int, float]]:
        """Get comprehensive cache statistics"""
        total_hits = self.redis_hits + self.memory_hits
        total_misses = self.redis_misses + self.memory_misses
        total_requests = total_hits + total_misses

        if total_requests == 0:
            hit_rate = 0.0
            redis_hit_rate = 0.0
        else:
            hit_rate = total_hits / total_requests
            redis_hit_rate = self.redis_hits / total_requests

        return {
            "total_operations": self.total_operations,
            "redis_hits": self.redis_hits,
            "redis_misses": self.redis_misses,
            "memory_hits": self.memory_hits,
            "memory_misses": self.memory_misses,
            "redis_errors": self.redis_errors,
            "hit_rate": hit_rate,
            "redis_hit_rate": redis_hit_rate,
            "total_hits": total_hits,
            "total_misses": total_misses,
        }


class RedisCache:
    """Redis-based cache with in-memory fallback for semantic similarity results"""

    def __init__(self):
        self.redis: Optional[Redis] = None
        self.memory_cache: Dict[str, Any] = {}
        self.connection_pool = None
        self.stats = CacheStats()
        self._last_health_check = 0
        self._redis_healthy = False
        self._connection_lock = asyncio.Lock()

    async def _create_connection(self) -> Optional[Redis]:
        """Create Redis connection with proper error handling"""
        try:
            if REDIS_URL:
                # Use Redis URL if provided
                redis_client = aioredis.from_url(
                    REDIS_URL,
                    max_connections=REDIS_MAX_CONNECTIONS,
                    socket_connect_timeout=REDIS_SOCKET_CONNECT_TIMEOUT,
                    socket_keepalive=REDIS_SOCKET_KEEPALIVE,
                    retry_on_timeout=REDIS_RETRY_ON_TIMEOUT,
                    decode_responses=False,  # We handle decoding manually
                )
            else:
                # Use individual Redis configuration
                redis_client = aioredis.Redis(
                    host=REDIS_HOST,
                    port=REDIS_PORT,
                    password=REDIS_PASSWORD if REDIS_PASSWORD else None,
                    db=REDIS_DB,
                    max_connections=REDIS_MAX_CONNECTIONS,
                    socket_connect_timeout=REDIS_SOCKET_CONNECT_TIMEOUT,
                    socket_keepalive=REDIS_SOCKET_KEEPALIVE,
                    retry_on_timeout=REDIS_RETRY_ON_TIMEOUT,
                    decode_responses=False,  # We handle decoding manually
                )

            # Test connection
            await redis_client.ping()
            logger.info("Redis connection established successfully")
            return redis_client

        except (ConnectionError, TimeoutError, RedisError) as e:
            logger.warning(f"Failed to connect to Redis: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            return None

    async def _ensure_connection(self) -> bool:
        """Ensure Redis connection is healthy, with periodic health checks"""
        if not REDIS_ENABLED:
            return False

        current_time = time.time()

        # Check if we need to perform a health check
        if (
            current_time - self._last_health_check > REDIS_HEALTH_CHECK_INTERVAL
            or not self._redis_healthy
        ):
            async with self._connection_lock:
                # Double-check after acquiring lock
                if (
                    current_time - self._last_health_check > REDIS_HEALTH_CHECK_INTERVAL
                    or not self._redis_healthy
                ):
                    try:
                        if self.redis is None:
                            self.redis = await self._create_connection()

                        if self.redis:
                            # Test the connection with timeout
                            await asyncio.wait_for(self.redis.ping(), timeout=2.0)
                            self._redis_healthy = True
                            logger.debug("Redis health check passed")
                        else:
                            self._redis_healthy = False

                    except Exception as e:
                        logger.debug(f"Redis health check failed: {e}")
                        self._redis_healthy = False
                        self.redis = None

                    self._last_health_check = current_time

        return self._redis_healthy and self.redis is not None

    def _get_cache_key(self, value1: str, value2: str, field_type: str) -> str:
        """Generate cache key for semantic comparison with application prefix"""
        # Normalize values for consistent caching
        v1 = value1.strip().lower()
        v2 = value2.strip().lower()
        # Sort to make cache key order-independent
        if v1 > v2:
            v1, v2 = v2, v1
        return f"{REDIS_KEY_PREFIX}:semantic:{v1}|{v2}|{field_type}"

    @lru_cache(maxsize=SEMANTIC_CACHE_SIZE)
    def _cached_exact_match(self, value1: str, value2: str) -> bool:
        """Fast cached exact string matching"""
        return value1.strip().lower() == value2.strip().lower()

    def _add_prefix_to_key(self, key: str) -> str:
        """Add application prefix to cache key if not already present"""
        if not key.startswith(REDIS_KEY_PREFIX):
            return f"{REDIS_KEY_PREFIX}:{key}"
        return key

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache (Redis first, then memory fallback)"""
        try:
            # Add prefix for Redis key
            redis_key = self._add_prefix_to_key(key)

            # Try Redis first if available (with timeout protection)
            if await self._ensure_connection():
                try:
                    result = await asyncio.wait_for(
                        self.redis.get(redis_key), timeout=1.0
                    )
                    if result is not None:
                        self.stats.redis_hit()
                        # Deserialize JSON result
                        return json.loads(result.decode("utf-8"))
                    else:
                        self.stats.redis_miss()
                except (RedisError, json.JSONDecodeError, asyncio.TimeoutError) as e:
                    logger.debug(f"Redis get error for key {redis_key}: {e}")
                    self.stats.redis_error()
                    # Mark Redis as unhealthy if we get errors
                    if isinstance(e, (RedisError, asyncio.TimeoutError)):
                        self._redis_healthy = False

            # Fallback to memory cache (use original key for memory)
            if key in self.memory_cache:
                self.stats.memory_hit()
                return self.memory_cache[key]
            else:
                self.stats.memory_miss()
                return None

        except Exception as e:
            logger.error(f"Unexpected error getting cache key {key}: {e}")
            self.stats.redis_error()
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache (Redis and memory)"""
        try:
            cache_ttl = ttl or REDIS_TTL
            redis_key = self._add_prefix_to_key(key)

            # Try to set in Redis first (with timeout protection)
            redis_success = False
            if await self._ensure_connection():
                try:
                    serialized_value = json.dumps(value)
                    await asyncio.wait_for(
                        self.redis.setex(redis_key, cache_ttl, serialized_value),
                        timeout=1.0,
                    )
                    redis_success = True
                except (RedisError, json.JSONEncodeError, asyncio.TimeoutError) as e:
                    logger.debug(f"Redis set error for key {redis_key}: {e}")
                    self.stats.redis_error()
                    # Mark Redis as unhealthy if we get errors
                    if isinstance(e, (RedisError, asyncio.TimeoutError)):
                        self._redis_healthy = False

            # Always set in memory cache as fallback (use original key)
            self.memory_cache[key] = value

            # Limit memory cache size to prevent unbounded growth
            if len(self.memory_cache) > SEMANTIC_CACHE_SIZE:
                # Remove oldest entries (simple FIFO)
                keys_to_remove = list(self.memory_cache.keys())[:-SEMANTIC_CACHE_SIZE]
                for old_key in keys_to_remove:
                    del self.memory_cache[old_key]

            return redis_success

        except Exception as e:
            logger.error(f"Unexpected error setting cache key {key}: {e}")
            return False

    async def get_semantic_similarity(
        self, value1: str, value2: str, field_type: str
    ) -> Optional[bool]:
        """Get cached semantic similarity result"""
        if not value1 or not value2:
            return None

        # Quick exact match check
        if self._cached_exact_match(value1, value2):
            return True

        cache_key = self._get_cache_key(value1, value2, field_type)
        return await self.get(cache_key)

    async def set_semantic_similarity(
        self, value1: str, value2: str, field_type: str, result: bool
    ) -> bool:
        """Cache semantic similarity result"""
        cache_key = self._get_cache_key(value1, value2, field_type)
        return await self.set(cache_key, result)

    async def get_multiple_semantic_similarities(
        self, comparisons: List[Tuple[str, str, str]]
    ) -> Dict[Tuple[str, str, str], Optional[bool]]:
        """Get multiple semantic similarity results in batch"""
        results = {}

        # Prepare all cache keys
        cache_keys = []
        for comparison in comparisons:
            value1, value2, field_type = comparison
            if not value1 or not value2:
                results[comparison] = None
                continue

            # Quick exact match check
            if self._cached_exact_match(value1, value2):
                results[comparison] = True
                continue

            cache_key = self._get_cache_key(value1, value2, field_type)
            cache_keys.append((comparison, cache_key))

        # Batch get from Redis if available (with timeout protection)
        if cache_keys and await self._ensure_connection():
            try:
                pipe = self.redis.pipeline()
                redis_keys = []
                for _, cache_key in cache_keys:
                    # Cache key already has prefix from _get_cache_key
                    redis_keys.append(cache_key)
                    pipe.get(cache_key)

                redis_results = await asyncio.wait_for(pipe.execute(), timeout=2.0)

                for i, (comparison, cache_key) in enumerate(cache_keys):
                    if i < len(redis_results) and redis_results[i] is not None:
                        try:
                            results[comparison] = json.loads(
                                redis_results[i].decode("utf-8")
                            )
                            self.stats.redis_hit()
                        except json.JSONDecodeError:
                            results[comparison] = None
                            self.stats.redis_error()
                    else:
                        # Check memory cache (use cache_key without prefix for memory)
                        memory_key = cache_key.replace(f"{REDIS_KEY_PREFIX}:", "")
                        if memory_key in self.memory_cache:
                            results[comparison] = self.memory_cache[memory_key]
                            self.stats.memory_hit()
                        else:
                            results[comparison] = None
                            self.stats.redis_miss()

            except Exception as e:
                logger.debug(f"Redis batch get error: {e}")
                self.stats.redis_error()
                # Mark Redis as unhealthy if we get errors
                if isinstance(e, (RedisError, asyncio.TimeoutError)):
                    self._redis_healthy = False
                # Fallback to individual memory cache checks
                for comparison, cache_key in cache_keys:
                    memory_key = cache_key.replace(f"{REDIS_KEY_PREFIX}:", "")
                    if memory_key in self.memory_cache:
                        results[comparison] = self.memory_cache[memory_key]
                        self.stats.memory_hit()
                    else:
                        results[comparison] = None
                        self.stats.memory_miss()
        else:
            # Fallback to memory cache only
            for comparison, cache_key in cache_keys:
                memory_key = cache_key.replace(f"{REDIS_KEY_PREFIX}:", "")
                if memory_key in self.memory_cache:
                    results[comparison] = self.memory_cache[memory_key]
                    self.stats.memory_hit()
                else:
                    results[comparison] = None
                    self.stats.memory_miss()

        return results

    async def set_multiple_semantic_similarities(
        self, comparisons: List[Tuple[Tuple[str, str, str], bool]]
    ) -> bool:
        """Set multiple semantic similarity results in batch"""
        try:
            # Prepare data for batch operations
            redis_operations = []
            for comparison, result in comparisons:
                value1, value2, field_type = comparison
                cache_key = self._get_cache_key(value1, value2, field_type)
                redis_operations.append((cache_key, result))
                # Always update memory cache (use key without prefix for memory)
                memory_key = cache_key.replace(f"{REDIS_KEY_PREFIX}:", "")
                self.memory_cache[memory_key] = result

            # Batch set in Redis if available (with timeout protection)
            if redis_operations and await self._ensure_connection():
                try:
                    pipe = self.redis.pipeline()
                    for cache_key, result in redis_operations:
                        serialized_value = json.dumps(result)
                        pipe.setex(cache_key, REDIS_TTL, serialized_value)

                    await asyncio.wait_for(pipe.execute(), timeout=2.0)
                    return True

                except Exception as e:
                    logger.debug(f"Redis batch set error: {e}")
                    self.stats.redis_error()
                    # Mark Redis as unhealthy if we get errors
                    if isinstance(e, (RedisError, asyncio.TimeoutError)):
                        self._redis_healthy = False

            # Limit memory cache size
            if len(self.memory_cache) > SEMANTIC_CACHE_SIZE:
                keys_to_remove = list(self.memory_cache.keys())[:-SEMANTIC_CACHE_SIZE]
                for old_key in keys_to_remove:
                    del self.memory_cache[old_key]

            return True

        except Exception as e:
            logger.error(f"Error in batch set semantic similarities: {e}")
            return False

    async def clear_cache(self) -> bool:
        """Clear all cached data with application prefix"""
        try:
            # Clear Redis cache if available (only keys with our prefix)
            redis_cleared = False
            if await self._ensure_connection():
                try:
                    # Use pattern matching to delete only our application's keys
                    pattern = f"{REDIS_KEY_PREFIX}:*"
                    cursor = 0
                    deleted_count = 0

                    # Use SCAN to find and delete keys with our prefix (with timeout protection)
                    while True:
                        cursor, keys = await asyncio.wait_for(
                            self.redis.scan(cursor, match=pattern, count=1000),
                            timeout=2.0,
                        )
                        if keys:
                            await asyncio.wait_for(
                                self.redis.delete(*keys), timeout=2.0
                            )
                            deleted_count += len(keys)
                        if cursor == 0:
                            break

                    redis_cleared = True
                    logger.info(
                        f"Redis cache cleared successfully ({deleted_count} keys deleted)"
                    )
                except Exception as e:
                    logger.debug(f"Failed to clear Redis cache: {e}")
                    self.stats.redis_error()
                    # Mark Redis as unhealthy if we get errors
                    if isinstance(e, (RedisError, asyncio.TimeoutError)):
                        self._redis_healthy = False

            # Clear memory cache
            self.memory_cache.clear()
            logger.info("Memory cache cleared successfully")

            return redis_cleared

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False

    async def get_cache_info(self) -> Dict[str, Any]:
        """Get comprehensive cache information"""
        info = {
            "redis_enabled": REDIS_ENABLED,
            "redis_healthy": self._redis_healthy,
            "memory_cache_size": len(self.memory_cache),
            "stats": self.stats.get_stats(),
        }

        # Add Redis-specific info if available (with timeout protection)
        if await self._ensure_connection():
            try:
                redis_info = await asyncio.wait_for(self.redis.info(), timeout=1.0)
                info["redis_info"] = {
                    "used_memory": redis_info.get("used_memory", 0),
                    "used_memory_human": redis_info.get("used_memory_human", "0B"),
                    "keyspace": redis_info.get(f"db{REDIS_DB}", {}),
                    "connected_clients": redis_info.get("connected_clients", 0),
                }
            except Exception as e:
                logger.debug(f"Failed to get Redis info: {e}")
                info["redis_info"] = {"error": str(e)}
                # Mark Redis as unhealthy if we get errors
                if isinstance(e, (RedisError, asyncio.TimeoutError)):
                    self._redis_healthy = False

        return info

    def _get_normalization_cache_key(self, cv_data: Dict[str, Any]) -> str:
        """Generate cache key for CV normalization result"""
        # Create a stable hash of the CV structure
        import hashlib

        cv_json = json.dumps(cv_data, sort_keys=True, ensure_ascii=False)
        cv_hash = hashlib.md5(cv_json.encode()).hexdigest()
        return f"{REDIS_KEY_PREFIX}:normalization:{cv_hash}"

    async def get_normalization_result(
        self, cv_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Get cached normalization result for CV data"""
        if not cv_data:
            return None

        cache_key = self._get_normalization_cache_key(cv_data)

        # Try Redis first (with timeout protection)
        if await self._ensure_connection():
            try:
                result = await asyncio.wait_for(self.redis.get(cache_key), timeout=1.0)
                if result:
                    try:
                        normalized_data = json.loads(result.decode("utf-8"))
                        self.stats.redis_hit()
                        return normalized_data
                    except json.JSONDecodeError:
                        self.stats.redis_error()
                        logger.warning(
                            f"Invalid JSON in normalization cache for key: {cache_key}"
                        )
                else:
                    self.stats.redis_miss()
            except Exception as e:
                logger.debug(f"Redis normalization cache lookup failed: {e}")
                self.stats.redis_error()
                # Mark Redis as unhealthy if we get errors
                if isinstance(e, (RedisError, asyncio.TimeoutError)):
                    self._redis_healthy = False

        # Try memory cache
        memory_key = cache_key.replace(f"{REDIS_KEY_PREFIX}:", "")
        if memory_key in self.memory_cache:
            result = self.memory_cache[memory_key]
            self.stats.memory_hit()
            return result
        else:
            self.stats.memory_miss()

        return None

    async def set_normalization_result(
        self, cv_data: Dict[str, Any], normalized_data: Dict[str, Any]
    ) -> bool:
        """Cache normalization result for CV data"""
        if not cv_data or not normalized_data:
            return False

        cache_key = self._get_normalization_cache_key(cv_data)
        value = json.dumps(normalized_data, ensure_ascii=False)

        # Store in memory cache
        memory_key = cache_key.replace(f"{REDIS_KEY_PREFIX}:", "")
        self.memory_cache[memory_key] = normalized_data

        # Store in Redis (with timeout protection)
        if await self._ensure_connection():
            try:
                await asyncio.wait_for(
                    self.redis.setex(cache_key, REDIS_TTL, value), timeout=2.0
                )
                return True
            except Exception as e:
                logger.debug(f"Failed to cache normalization result in Redis: {e}")
                self.stats.redis_error()
                # Mark Redis as unhealthy if we get errors
                if isinstance(e, (RedisError, asyncio.TimeoutError)):
                    self._redis_healthy = False

        return False

    async def close(self):
        """Close Redis connection"""
        if self.redis:
            try:
                await asyncio.wait_for(self.redis.close(), timeout=2.0)
                logger.info("Redis connection closed")
            except Exception as e:
                logger.debug(f"Error closing Redis connection: {e}")
            finally:
                self.redis = None
                self._redis_healthy = False


# Global cache instance
_cache_instance: Optional[RedisCache] = None


async def get_cache() -> RedisCache:
    """Get or create global cache instance"""
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = RedisCache()
    return _cache_instance


async def close_cache():
    """Close global cache instance"""
    global _cache_instance
    if _cache_instance:
        await _cache_instance.close()
        _cache_instance = None

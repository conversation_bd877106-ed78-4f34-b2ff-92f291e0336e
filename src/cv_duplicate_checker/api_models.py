"""
API Models for CV Duplicate Checker FastAPI endpoints
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class CVData(BaseModel):
    """CV data structure for API requests"""

    education: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Education entries"
    )
    experience: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Work experience entries"
    )
    extra_curricular: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Extra-curricular activities"
    )
    certification: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Certifications"
    )
    language: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Language skills"
    )
    achievement: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Achievements and awards"
    )
    reference: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="References"
    )
    external_doc: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="External documents/links"
    )
    summary: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Summary sections"
    )
    hobby: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Hobbies and interests"
    )


class CVCheckRequest(BaseModel):
    """Request model for CV duplicate checking endpoint"""

    existing_cv: CVData = Field(description="Existing CV data to compare against")
    new_data: CVData = Field(description="New CV data to be processed")

    class Config:
        json_schema_extra = {
            "example": {
                "existing_cv": {
                    "education": [
                        {
                            "school": "DHBK",
                            "major": "Computer Science",
                            "start_date": "2020",
                        }
                    ],
                    "experience": [
                        {
                            "title": "Software Engineer",
                            "company": "Google",
                            "start_date": "2022-01-01",
                        }
                    ],
                },
                "new_data": {
                    "education": [
                        {
                            "school": "Đại học Bách Khoa",
                            "major": "Computer Science",
                            "start_date": "2020",
                            "degree": "Bachelor",
                            "gpa": 3.8,
                        }
                    ],
                    "experience": [
                        {
                            "title": "Software Engineer",
                            "company": "Google LLC",
                            "start_date": "2022-01-01",
                            "end_date": "2023-12-31",
                            "skills": ["Python", "Go"],
                        }
                    ],
                },
            }
        }


class ActionDetail(BaseModel):
    """Detailed information about a specific action taken"""

    action: str = Field(description="Type of action: add, update, or skip")
    item: Dict[str, Any] = Field(description="The processed item")
    existing_index: Optional[int] = Field(
        default=None, description="Index in existing CV (for updates)"
    )
    fields_to_update: Optional[List[str]] = Field(
        default=None, description="Fields that were updated"
    )
    reason: str = Field(description="Reason for this action")
    section: str = Field(description="CV section name")


class ActionSummary(BaseModel):
    """Summary of all actions taken"""

    total_actions: int = Field(description="Total number of actions performed")
    added: int = Field(description="Number of new items added")
    updated: int = Field(description="Number of existing items updated")
    skipped: int = Field(description="Number of duplicate items skipped")


class SectionSummary(BaseModel):
    """Summary for a specific CV section"""

    added: int = Field(description="Items added in this section")
    updated: int = Field(description="Items updated in this section")
    skipped: int = Field(description="Items skipped in this section")


class NormalizationInfo(BaseModel):
    """Information about data normalization process"""

    existing_cv_normalized: bool = Field(
        description="Whether existing CV was normalized"
    )
    new_data_normalized: bool = Field(description="Whether new data was normalized")
    normalization_time: float = Field(
        description="Time spent on normalization (seconds)"
    )
    cached_normalizations: int = Field(
        description="Number of cached normalization results used"
    )


class CVCheckResponse(BaseModel):
    """Response model for CV duplicate checking endpoint"""

    processed_cv: CVData = Field(description="The final processed CV with merged data")
    action_summary: ActionSummary = Field(description="Summary of all actions taken")
    section_summary: Dict[str, SectionSummary] = Field(
        description="Summary by CV section"
    )
    detailed_actions: List[ActionDetail] = Field(
        description="Detailed list of all actions performed"
    )
    normalization_info: Optional[NormalizationInfo] = Field(
        default=None, description="Information about structure normalization"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "processed_cv": {
                    "education": [
                        {
                            "school": "Đại học Bách Khoa",
                            "major": "Computer Science",
                            "start_date": "2020",
                            "degree": "Bachelor",
                            "gpa": 3.8,
                        }
                    ]
                },
                "action_summary": {
                    "total_actions": 2,
                    "added": 0,
                    "updated": 1,
                    "skipped": 1,
                },
                "section_summary": {
                    "education": {"added": 0, "updated": 1, "skipped": 0}
                },
                "detailed_actions": [
                    {
                        "action": "update",
                        "item": {
                            "school": "Đại học Bách Khoa",
                            "major": "Computer Science",
                            "start_date": "2020",
                            "degree": "Bachelor",
                            "gpa": 3.8,
                        },
                        "existing_index": 0,
                        "fields_to_update": ["degree", "gpa"],
                        "reason": "Bổ sung thông tin thiếu: degree, gpa (phát hiện bằng semantic matching)",
                        "section": "education",
                    }
                ],
            }
        }


class HealthResponse(BaseModel):
    """Health check response model"""

    status: str = Field(description="Service status")
    version: str = Field(description="API version")
    agent_ready: bool = Field(
        description="Whether the CV duplicate checker agent is ready"
    )
    redis_healthy: Optional[bool] = Field(
        default=None, description="Whether Redis cache is healthy and connected"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "agent_ready": True,
                "redis_healthy": True,
            }
        }


class CacheStatsResponse(BaseModel):
    """Cache statistics response model"""

    redis_enabled: bool = Field(description="Whether Redis caching is enabled")
    redis_healthy: bool = Field(description="Whether Redis connection is healthy")
    memory_cache_size: int = Field(description="Number of items in memory cache")
    stats: Dict[str, Any] = Field(description="Cache performance statistics")
    redis_info: Optional[Dict[str, Any]] = Field(
        default=None, description="Redis server information"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "redis_enabled": True,
                "redis_healthy": True,
                "memory_cache_size": 150,
                "stats": {
                    "total_operations": 1000,
                    "redis_hits": 450,
                    "redis_misses": 350,
                    "memory_hits": 100,
                    "memory_misses": 100,
                    "redis_errors": 2,
                    "hit_rate": 0.55,
                    "redis_hit_rate": 0.45,
                },
                "redis_info": {
                    "used_memory": 1048576,
                    "used_memory_human": "1.00M",
                    "connected_clients": 2,
                },
            }
        }


class CacheOperationResponse(BaseModel):
    """Cache operation response model"""

    success: bool = Field(description="Whether the operation was successful")
    message: str = Field(description="Operation result message")
    details: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional operation details"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "Cache cleared successfully",
                "details": {"redis_cleared": True, "memory_cleared": True},
            }
        }


class ErrorResponse(BaseModel):
    """Error response model"""

    error: str = Field(description="Error type")
    message: str = Field(description="Detailed error message")
    details: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional error details"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "Invalid CV data provided",
                "details": {
                    "field": "education",
                    "issue": "Missing required field 'school'",
                },
            }
        }

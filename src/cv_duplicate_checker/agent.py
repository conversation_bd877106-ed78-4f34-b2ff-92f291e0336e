"""CV Duplicate Checker Agent using LangGraph and OpenAI LLM"""

import asyncio
import json
import logging
import time
from functools import lru_cache
from typing import Any, Dict, List, Tuple

from dotenv import load_dotenv
from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import Chat<PERSON>pen<PERSON><PERSON>
from langgraph.graph import END, START, StateGraph
from langgraph.graph.state import CompiledStateGraph

from .config import (
    BATCH_SIZE,
    DEFAULT_MODEL,
    DEFAULT_TEMPERATURE,
    DUPLICATE_CRITERIA,
    ENABLE_BATCH_PROCESSING,
    ENABLE_EARLY_EXIT,
    ENABLE_SEMANTIC_CACHE,
    FIELD_TYPE_MAPPING,
    LLM_MAX_RETRIES,
    LLM_TIMEOUT,
    MAX_CONCURRENT_BATCHES,
    MAX_TOTAL_ACTIONS,
    SEMANTIC_CACHE_SIZE,
    UPDATABLE_FIELDS,
)
from .models import AgentState, InputAgentState, OutputAgentState
from langfuse.langchain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .cache import get_cache
from .llm_normalizer import LLMStructureNormalizer

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BatchSemanticChecker:
    """Optimized batch semantic similarity checker with Redis caching"""

    def __init__(self, llm: ChatOpenAI):
        self.llm = llm
        self.cache = None  # Will be initialized async
        self._cache_initialized = False

    async def _ensure_cache(self):
        """Ensure cache is initialized"""
        if not self._cache_initialized:
            self.cache = await get_cache()
            self._cache_initialized = True

    @lru_cache(maxsize=SEMANTIC_CACHE_SIZE)
    def _cached_exact_match(self, value1: str, value2: str) -> bool:
        """Fast cached exact string matching"""
        return value1.strip().lower() == value2.strip().lower()

    async def _batch_semantic_similarity(
        self, comparisons: List[Tuple[str, str, str]]
    ) -> List[bool]:
        """
        Perform batch semantic similarity checks

        Args:
            comparisons: List of (value1, value2, field_type) tuples

        Returns:
            List of boolean results in same order as input
        """
        if not comparisons:
            return []

        try:
            # Build batch prompt
            comparison_texts = []
            for i, (v1, v2, field_type) in enumerate(comparisons):
                comparison_texts.append(
                    f"{i+1}. Value 1: {v1}\n   Value 2: {v2}\n   Field type: {field_type}"
                )

            system_prompt = """
You are a semantic analysis expert. Your task is to determine if pairs of values are semantically equivalent.

Cases to consider as equivalent:
- Abbreviations and full names (e.g., DHBK and Đại học Bách Khoa)
- Variations of the same name (e.g., MIT and Massachusetts Institute of Technology)  
- Different spellings of the same organization/school/company
- Equivalent technical terms (e.g., Computer Science and Khoa học máy tính)

For each comparison, respond with only 'true' or 'false' on separate lines, in the same order as the questions.
"""

            human_prompt = f"Compare these {len(comparisons)} pairs:\n\n" + "\n\n".join(
                comparison_texts
            )

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=human_prompt),
            ]

            response = await self.llm.ainvoke(messages)
            results_text = response.content.strip()

            # Parse results
            lines = [
                line.strip().lower()
                for line in results_text.split("\n")
                if line.strip()
            ]
            results = []

            for i, line in enumerate(lines[: len(comparisons)]):
                results.append(line == "true")

            # Fill any missing results with False
            while len(results) < len(comparisons):
                results.append(False)

            return results[: len(comparisons)]

        except Exception as e:
            logger.warning(f"Batch semantic comparison failed: {e}. Using fallback.")
            # Fallback to exact matches
            return [self._cached_exact_match(v1, v2) for v1, v2, _ in comparisons]

    async def check_similarities(
        self, comparisons: List[Tuple[str, str, str]]
    ) -> List[bool]:
        """
        Check semantic similarities with Redis caching and batching

        Args:
            comparisons: List of (value1, value2, field_type) tuples

        Returns:
            List of boolean results
        """
        if not comparisons:
            return []

        # Ensure cache is initialized
        await self._ensure_cache()

        results = []
        uncached_comparisons = []
        uncached_indices = []

        # Check cache first using batch operations for better performance
        if ENABLE_SEMANTIC_CACHE:
            # Get all cached results in batch
            cached_results = await self.cache.get_multiple_semantic_similarities(
                comparisons
            )

            for i, comparison in enumerate(comparisons):
                v1, v2, field_type = comparison

                if not v1 or not v2:
                    results.append(False)
                    continue

                # Quick exact match check
                if self._cached_exact_match(v1, v2):
                    results.append(True)
                    continue

                # Check if we have a cached result
                cached_result = cached_results.get(comparison)
                if cached_result is not None:
                    results.append(cached_result)
                else:
                    results.append(None)  # Placeholder
                    uncached_comparisons.append(comparison)
                    uncached_indices.append(i)
        else:
            # No caching enabled, mark all as uncached except exact matches
            for i, (v1, v2, field_type) in enumerate(comparisons):
                if not v1 or not v2:
                    results.append(False)
                    continue

                # Quick exact match check
                if self._cached_exact_match(v1, v2):
                    results.append(True)
                    continue

                results.append(None)  # Placeholder
                uncached_comparisons.append((v1, v2, field_type))
                uncached_indices.append(i)

        # Process uncached comparisons in batches
        if uncached_comparisons and ENABLE_BATCH_PROCESSING:
            batches = [
                uncached_comparisons[i : i + BATCH_SIZE]
                for i in range(0, len(uncached_comparisons), BATCH_SIZE)
            ]

            # Process batches concurrently
            batch_tasks = []
            for batch in batches[:MAX_CONCURRENT_BATCHES]:
                batch_tasks.append(self._batch_semantic_similarity(batch))

            if len(batches) > MAX_CONCURRENT_BATCHES:
                # Process remaining batches sequentially
                for batch in batches[MAX_CONCURRENT_BATCHES:]:
                    batch_tasks.append(self._batch_semantic_similarity(batch))

            try:
                batch_results = await asyncio.gather(
                    *batch_tasks, return_exceptions=True
                )

                # Flatten results and handle exceptions
                all_batch_results = []
                for batch_result in batch_results:
                    if isinstance(batch_result, Exception):
                        logger.error(f"Batch processing error: {batch_result}")
                        # Add False results for failed batch
                        batch_size = len(batches[len(all_batch_results)])
                        all_batch_results.extend([False] * batch_size)
                    else:
                        all_batch_results.extend(batch_result)

                # Update results and cache
                cache_operations = []
                for i, result in enumerate(all_batch_results[: len(uncached_indices)]):
                    idx = uncached_indices[i]
                    results[idx] = result

                    # Prepare cache operation
                    if ENABLE_SEMANTIC_CACHE and i < len(uncached_comparisons):
                        comparison = uncached_comparisons[i]
                        cache_operations.append((comparison, result))

                # Batch cache the results
                if cache_operations:
                    await self.cache.set_multiple_semantic_similarities(
                        cache_operations
                    )

            except Exception as e:
                logger.error(f"Batch processing failed: {e}")
                # Fallback to exact matches for uncached items
                for i in uncached_indices:
                    v1, v2, _ = comparisons[i]
                    results[i] = self._cached_exact_match(v1, v2)

        elif uncached_comparisons:
            # Fallback to individual processing if batching disabled
            cache_operations = []
            for i, (v1, v2, field_type) in enumerate(uncached_comparisons):
                idx = uncached_indices[i]
                result = self._cached_exact_match(v1, v2)
                results[idx] = result

                if ENABLE_SEMANTIC_CACHE:
                    cache_operations.append(((v1, v2, field_type), result))

            # Batch cache the fallback results
            if cache_operations:
                await self.cache.set_multiple_semantic_similarities(cache_operations)

        return results

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics from Redis cache"""
        await self._ensure_cache()
        return await self.cache.get_cache_info()


class CVDuplicateCheckerAgent:
    """CV Duplicate Checker Agent with semantic matching capabilities"""

    def __init__(
        self,
        model_name: str = DEFAULT_MODEL,
        temperature: float = DEFAULT_TEMPERATURE,
        timeout: int = LLM_TIMEOUT,
    ):
        """Initialize the agent with LLM and workflow graph"""
        self.llm = ChatOpenAI(
            model=model_name,
            temperature=temperature,
            request_timeout=timeout,
            max_retries=LLM_MAX_RETRIES,
        )
        self.semantic_checker = BatchSemanticChecker(self.llm)
        self.normalizer = LLMStructureNormalizer(self.llm)
        self.graph: CompiledStateGraph = self._create_graph()
        self._performance_metrics = {
            "total_processing_time": 0,
            "semantic_checks": 0,
            "normalization_time": 0,
            "cache_stats": {},
        }

    def _create_graph(self) -> CompiledStateGraph:
        """Create LangGraph workflow with parallel processing"""
        workflow = StateGraph(
            AgentState, input=InputAgentState, output=OutputAgentState
        )

        # Add nodes
        workflow.add_node("parse_input", self._parse_input)
        workflow.add_node("check_education", self._check_education)
        workflow.add_node("check_experience", self._check_experience)
        workflow.add_node("check_extra_curricular", self._check_extra_curricular)
        workflow.add_node("check_certification", self._check_certification)
        workflow.add_node("check_language", self._check_language)
        workflow.add_node("check_achievement", self._check_achievement)
        workflow.add_node("check_reference", self._check_reference)
        workflow.add_node("check_external_doc", self._check_external_doc)
        workflow.add_node("check_summary", self._check_summary)
        workflow.add_node("check_hobby", self._check_hobby)
        workflow.add_node("merge_results", self._merge_results)

        # Define parallel workflow
        workflow.add_edge(START, "parse_input")

        # From parse_input, all sections run in parallel
        workflow.add_edge("parse_input", "check_education")
        workflow.add_edge("parse_input", "check_experience")
        workflow.add_edge("parse_input", "check_extra_curricular")
        workflow.add_edge("parse_input", "check_certification")
        workflow.add_edge("parse_input", "check_language")
        workflow.add_edge("parse_input", "check_achievement")
        workflow.add_edge("parse_input", "check_reference")
        workflow.add_edge("parse_input", "check_external_doc")
        workflow.add_edge("parse_input", "check_summary")
        workflow.add_edge("parse_input", "check_hobby")

        # All sections lead to merge_results
        workflow.add_edge("check_education", "merge_results")
        workflow.add_edge("check_experience", "merge_results")
        workflow.add_edge("check_extra_curricular", "merge_results")
        workflow.add_edge("check_certification", "merge_results")
        workflow.add_edge("check_language", "merge_results")
        workflow.add_edge("check_achievement", "merge_results")
        workflow.add_edge("check_reference", "merge_results")
        workflow.add_edge("check_external_doc", "merge_results")
        workflow.add_edge("check_summary", "merge_results")
        workflow.add_edge("check_hobby", "merge_results")

        # merge_results to END
        workflow.add_edge("merge_results", END)

        return workflow.compile()

    async def _parse_input(self, state: AgentState) -> Dict[str, Any]:
        """Parse input, normalize structures, and initialize processed_cv"""
        logger.info("Parsing and normalizing input data...")

        start_time = time.time()

        try:
            # Normalize both CV structures using LLM
            (
                normalized_existing,
                normalized_new,
            ) = await self.normalizer.normalize_cv_pair(
                state["existing_cv"], state["new_data"]
            )

            normalization_time = time.time() - start_time
            self._performance_metrics["normalization_time"] = normalization_time

            logger.info(f"CV normalization completed in {normalization_time:.2f}s")

            # Return normalized data
            return {
                "existing_cv": normalized_existing,
                "new_data": normalized_new,
                "processed_cv": normalized_existing.copy(),
                "analysis_results": [],
                # Initialize section results
                "education_result": None,
                "experience_result": None,
                "extra_curricular_result": None,
                "certification_result": None,
                "language_result": None,
                "achievement_result": None,
                "reference_result": None,
                "external_doc_result": None,
                "summary_result": None,
                "hobby_result": None,
            }

        except Exception as e:
            logger.warning(f"Normalization failed: {e}. Using original data.")
            # Fallback to original behavior if normalization fails
            return {
                "processed_cv": state["existing_cv"].copy(),
                "analysis_results": [],
                # Initialize section results
                "education_result": None,
                "experience_result": None,
                "extra_curricular_result": None,
                "certification_result": None,
                "language_result": None,
                "achievement_result": None,
                "reference_result": None,
                "external_doc_result": None,
                "summary_result": None,
                "hobby_result": None,
            }

    async def _check_parallel_duplicates(
        self,
        new_items: List[Dict],
        existing_items: List[Dict],
        duplicate_criteria: List[str],
    ) -> List[Tuple[int, int, bool]]:
        """
        Check duplicates in parallel for all new vs existing item pairs

        Args:
            new_items: New items to check
            existing_items: Existing items
            duplicate_criteria: Fields that determine duplicates

        Returns:
            List of (new_idx, existing_idx, is_duplicate) tuples
        """
        if not new_items or not existing_items:
            return []

        # Collect all semantic comparisons needed
        comparisons = []
        comparison_metadata = []

        for new_idx, new_item in enumerate(new_items):
            for existing_idx, existing_item in enumerate(existing_items):
                item_comparisons = []

                for field in duplicate_criteria:
                    new_value = new_item.get(field)
                    existing_value = existing_item.get(field)

                    if new_value is None or existing_value is None:
                        # Handle None values - they must both be None to match
                        if new_value != existing_value:
                            # Early exit - this pair can't be duplicate
                            item_comparisons = []
                            break
                        continue

                    # Only add semantic comparisons for string fields
                    if isinstance(new_value, str) and isinstance(existing_value, str):
                        field_type = FIELD_TYPE_MAPPING.get(field, "general")
                        comparisons.append((new_value, existing_value, field_type))
                        item_comparisons.append(len(comparisons) - 1)
                    else:
                        # For non-string fields, check exact equality immediately
                        if new_value != existing_value:
                            # Early exit - this pair can't be duplicate
                            item_comparisons = []
                            break

                comparison_metadata.append(
                    {
                        "new_idx": new_idx,
                        "existing_idx": existing_idx,
                        "comparison_indices": item_comparisons,
                    }
                )

        # Perform all semantic comparisons at once
        if comparisons:
            start_time = time.time()
            semantic_results = await self.semantic_checker.check_similarities(
                comparisons
            )
            self._performance_metrics["semantic_checks"] += len(comparisons)
            logger.info(
                f"Processed {len(comparisons)} semantic comparisons in {time.time() - start_time:.2f}s"
            )
        else:
            semantic_results = []

        # Determine duplicates based on results
        duplicate_pairs = []

        for metadata in comparison_metadata:
            if not metadata["comparison_indices"]:
                # This pair had early exit (non-matching None or non-string fields)
                continue

            # Check if all semantic comparisons for this pair are True
            is_duplicate = all(
                semantic_results[idx] for idx in metadata["comparison_indices"]
            )

            if is_duplicate:
                duplicate_pairs.append(
                    (metadata["new_idx"], metadata["existing_idx"], True)
                )

        return duplicate_pairs

    async def test_semantic_similarity(
        self, value1: str, value2: str, field_type: str = "general"
    ) -> bool:
        """Test method for individual semantic similarity (for testing only)"""
        results = await self.semantic_checker.check_similarities(
            [(value1, value2, field_type)]
        )
        return results[0] if results else False

    async def _check_section_duplicates(
        self,
        section_name: str,
        new_items: List[Dict],
        existing_items: List[Dict],
        duplicate_criteria: List[str],
        updatable_fields: List[str],
    ) -> List[Dict]:
        """
        Optimized duplicate checking with parallel semantic matching

        Args:
            section_name: Section name (education, experience, etc.)
            new_items: New items to check
            existing_items: Existing items
            duplicate_criteria: Criteria to determine complete duplicates
            updatable_fields: Fields that can be updated

        Returns:
            List of actions to perform
        """
        if not new_items:
            return []

        start_time = time.time()

        # Get all duplicate pairs in parallel
        duplicate_pairs = await self._check_parallel_duplicates(
            new_items, existing_items, duplicate_criteria
        )

        # Create mapping of new_idx -> (existing_idx, is_duplicate)
        duplicate_map = {}
        for new_idx, existing_idx, is_duplicate in duplicate_pairs:
            if new_idx not in duplicate_map:
                duplicate_map[new_idx] = []
            duplicate_map[new_idx].append((existing_idx, is_duplicate))

        actions = []

        for new_idx, new_item in enumerate(new_items):
            duplicate_found = False

            # Check if this new item has any duplicates
            if new_idx in duplicate_map:
                for existing_idx, is_duplicate in duplicate_map[new_idx]:
                    if is_duplicate:
                        duplicate_found = True
                        existing_item = existing_items[existing_idx]

                        # Check if any fields need to be supplemented
                        fields_to_update = []
                        for field in updatable_fields:
                            if (
                                new_item.get(field) is not None
                                and existing_item.get(field) is None
                            ):
                                fields_to_update.append(field)

                        if fields_to_update:
                            # Update missing information
                            actions.append(
                                {
                                    "action": "update",
                                    "item": new_item,
                                    "existing_index": existing_idx,
                                    "fields_to_update": fields_to_update,
                                    "reason": f"Bổ sung thông tin thiếu: {', '.join(fields_to_update)} (phát hiện bằng batch semantic matching)",
                                    "section": section_name,
                                }
                            )
                        else:
                            # Complete duplicate, skip
                            actions.append(
                                {
                                    "action": "skip",
                                    "item": new_item,
                                    "reason": "Trùng lặp hoàn toàn (phát hiện bằng batch semantic matching)",
                                    "section": section_name,
                                }
                            )

                        # Use early exit if enabled - stop at first duplicate found
                        if ENABLE_EARLY_EXIT:
                            break

            if not duplicate_found:
                # Add new item
                actions.append(
                    {
                        "action": "add",
                        "item": new_item,
                        "reason": "Thông tin mới",
                        "section": section_name,
                    }
                )

        processing_time = time.time() - start_time
        logger.info(
            f"Section {section_name} processed in {processing_time:.2f}s with {len(actions)} actions"
        )

        return actions

    async def _check_education(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Education"""
        logger.info("Checking education duplicates...")

        new_education = state["new_data"].get("education", [])
        existing_education = state["processed_cv"].get("education", []).copy()

        actions = await self._check_section_duplicates(
            "education",
            new_education,
            existing_education,
            duplicate_criteria=DUPLICATE_CRITERIA["education"],
            updatable_fields=UPDATABLE_FIELDS["education"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_education.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_education[idx][field] = action["item"][field]

        return {"education_result": {"data": existing_education, "actions": actions}}

    async def _check_experience(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Experience"""
        logger.info("Checking experience duplicates...")

        new_experience = state["new_data"].get("experience", [])
        existing_experience = state["processed_cv"].get("experience", []).copy()

        actions = await self._check_section_duplicates(
            "experience",
            new_experience,
            existing_experience,
            duplicate_criteria=DUPLICATE_CRITERIA["experience"],
            updatable_fields=UPDATABLE_FIELDS["experience"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_experience.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_experience[idx][field] = action["item"][field]

        return {"experience_result": {"data": existing_experience, "actions": actions}}

    async def _check_extra_curricular(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Extra-curricular"""
        logger.info("Checking extra-curricular duplicates...")

        new_extra = state["new_data"].get("extra_curricular", [])
        existing_extra = state["processed_cv"].get("extra_curricular", []).copy()

        actions = await self._check_section_duplicates(
            "extra_curricular",
            new_extra,
            existing_extra,
            duplicate_criteria=DUPLICATE_CRITERIA["extra_curricular"],
            updatable_fields=UPDATABLE_FIELDS["extra_curricular"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_extra.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_extra[idx][field] = action["item"][field]

        return {"extra_curricular_result": {"data": existing_extra, "actions": actions}}

    async def _check_certification(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Certification"""
        logger.info("Checking certification duplicates...")

        new_cert = state["new_data"].get("certification", [])
        existing_cert = state["processed_cv"].get("certification", []).copy()

        actions = await self._check_section_duplicates(
            "certification",
            new_cert,
            existing_cert,
            duplicate_criteria=DUPLICATE_CRITERIA["certification"],
            updatable_fields=UPDATABLE_FIELDS["certification"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_cert.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_cert[idx][field] = action["item"][field]

        return {"certification_result": {"data": existing_cert, "actions": actions}}

    async def _check_language(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Language"""
        logger.info("Checking language duplicates...")

        new_lang = state["new_data"].get("language", [])
        existing_lang = state["processed_cv"].get("language", []).copy()

        actions = await self._check_section_duplicates(
            "language",
            new_lang,
            existing_lang,
            duplicate_criteria=DUPLICATE_CRITERIA["language"],
            updatable_fields=UPDATABLE_FIELDS["language"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_lang.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_lang[idx][field] = action["item"][field]

        return {"language_result": {"data": existing_lang, "actions": actions}}

    async def _check_achievement(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Achievement"""
        logger.info("Checking achievement duplicates...")

        new_achieve = state["new_data"].get("achievement", [])
        existing_achieve = state["processed_cv"].get("achievement", []).copy()

        actions = await self._check_section_duplicates(
            "achievement",
            new_achieve,
            existing_achieve,
            duplicate_criteria=DUPLICATE_CRITERIA["achievement"],
            updatable_fields=UPDATABLE_FIELDS["achievement"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_achieve.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_achieve[idx][field] = action["item"][field]

        return {"achievement_result": {"data": existing_achieve, "actions": actions}}

    async def _check_reference(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Reference"""
        logger.info("Checking reference duplicates...")

        new_ref = state["new_data"].get("reference", [])
        existing_ref = state["processed_cv"].get("reference", []).copy()

        actions = await self._check_section_duplicates(
            "reference",
            new_ref,
            existing_ref,
            duplicate_criteria=DUPLICATE_CRITERIA["reference"],
            updatable_fields=UPDATABLE_FIELDS["reference"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_ref.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_ref[idx][field] = action["item"][field]

        return {"reference_result": {"data": existing_ref, "actions": actions}}

    async def _check_external_doc(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for External Doc"""
        logger.info("Checking external doc duplicates...")

        new_doc = state["new_data"].get("external_doc", [])
        existing_doc = state["processed_cv"].get("external_doc", []).copy()

        actions = await self._check_section_duplicates(
            "external_doc",
            new_doc,
            existing_doc,
            duplicate_criteria=DUPLICATE_CRITERIA["external_doc"],
            updatable_fields=UPDATABLE_FIELDS["external_doc"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_doc.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_doc[idx][field] = action["item"][field]

        return {"external_doc_result": {"data": existing_doc, "actions": actions}}

    async def _check_summary(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Summary"""
        logger.info("Checking summary duplicates...")

        new_summary = state["new_data"].get("summary", [])
        existing_summary = state["processed_cv"].get("summary", []).copy()

        actions = await self._check_section_duplicates(
            "summary",
            new_summary,
            existing_summary,
            duplicate_criteria=DUPLICATE_CRITERIA["summary"],
            updatable_fields=UPDATABLE_FIELDS["summary"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_summary.append(action["item"])

        return {"summary_result": {"data": existing_summary, "actions": actions}}

    async def _check_hobby(self, state: AgentState) -> Dict[str, Any]:
        """Check duplicates for Hobby"""
        logger.info("Checking hobby duplicates...")

        new_hobby = state["new_data"].get("hobby", [])
        existing_hobby = state["processed_cv"].get("hobby", []).copy()

        actions = await self._check_section_duplicates(
            "hobby",
            new_hobby,
            existing_hobby,
            duplicate_criteria=DUPLICATE_CRITERIA["hobby"],
            updatable_fields=UPDATABLE_FIELDS["hobby"],
        )

        # Apply actions
        for action in actions:
            if action["action"] == "add":
                existing_hobby.append(action["item"])
            elif action["action"] == "update":
                idx = action["existing_index"]
                for field in action["fields_to_update"]:
                    existing_hobby[idx][field] = action["item"][field]

        return {"hobby_result": {"data": existing_hobby, "actions": actions}}

    async def _merge_results(self, state: AgentState) -> Dict[str, Any]:
        """Merge final results from section results"""
        logger.info("Merging final results...")

        # Collect all section results
        section_results = {
            "education": state.get("education_result"),
            "experience": state.get("experience_result"),
            "extra_curricular": state.get("extra_curricular_result"),
            "certification": state.get("certification_result"),
            "language": state.get("language_result"),
            "achievement": state.get("achievement_result"),
            "reference": state.get("reference_result"),
            "external_doc": state.get("external_doc_result"),
            "summary": state.get("summary_result"),
            "hobby": state.get("hobby_result"),
        }

        # Build processed_cv from section results
        processed_cv = state["processed_cv"].copy()
        all_actions = []

        for section_name, result in section_results.items():
            if result is not None:
                # Update processed_cv with data from section result
                processed_cv[section_name] = result["data"]

                # Collect actions with size limit
                section_actions = result.get("actions", [])
                if len(all_actions) + len(section_actions) <= MAX_TOTAL_ACTIONS:
                    all_actions.extend(section_actions)
                else:
                    # Add only what fits within the limit
                    remaining_slots = MAX_TOTAL_ACTIONS - len(all_actions)
                    if remaining_slots > 0:
                        all_actions.extend(section_actions[:remaining_slots])
                        logger.warning(
                            f"Total actions limit reached. Truncated actions from section {section_name}"
                        )
                    break

        # Create action summary
        action_summary = {
            "total_actions": len(all_actions),
            "added": len([a for a in all_actions if a["action"] == "add"]),
            "updated": len([a for a in all_actions if a["action"] == "update"]),
            "skipped": len([a for a in all_actions if a["action"] == "skip"]),
        }

        # Group actions by section
        section_summary = {}
        action_mapping = {"add": "added", "update": "updated", "skip": "skipped"}

        for action in all_actions:
            section = action.get("section", "unknown")
            if section not in section_summary:
                section_summary[section] = {"added": 0, "updated": 0, "skipped": 0}

            action_type = action_mapping.get(action["action"], action["action"])
            if action_type in section_summary[section]:
                section_summary[section][action_type] += 1

        # Update performance metrics
        self._performance_metrics[
            "cache_stats"
        ] = await self.semantic_checker.get_cache_stats()
        self._performance_metrics["normalization_stats"] = (
            self.normalizer.get_normalization_stats()
        )

        return {
            "final_result": {
                "processed_cv": processed_cv,
                "action_summary": action_summary,
                "section_summary": section_summary,
                "detailed_actions": all_actions,
                "performance_metrics": self._performance_metrics.copy(),
            }
        }

    async def process(self, existing_cv_json: str, new_data_json: str) -> dict:
        """
        Process CV duplicate checking with performance tracking

        Args:
            existing_cv_json: JSON string of existing CV
            new_data_json: JSON string of new data

        Returns:
            Dictionary of processed result with performance metrics
        """
        overall_start_time = time.time()

        try:
            # Parse JSON input
            existing_cv = json.loads(existing_cv_json)
            new_data = json.loads(new_data_json)

            # Create initial state
            initial_state = AgentState(
                existing_cv=existing_cv,
                new_data=new_data,
                processed_cv={},
                current_section=None,
                analysis_results=[],
                final_result=None,
                education_result=None,
                experience_result=None,
                extra_curricular_result=None,
                certification_result=None,
                language_result=None,
                achievement_result=None,
                reference_result=None,
                external_doc_result=None,
                summary_result=None,
                hobby_result=None,
            )

            # Create Langfuse callback handler
            callback_handler = CallbackHandler()

            # Run graph with tracing
            final_state = await self.graph.ainvoke(
                initial_state,
                config={
                    "callbacks": [callback_handler],
                    "run_name": "CV Duplicate Checker",
                },
            )

            # Extract result and add timing
            result_data = final_state["final_result"]
            total_time = time.time() - overall_start_time

            # Update performance metrics
            self._performance_metrics["total_processing_time"] = total_time
            result_data["performance_metrics"] = self._performance_metrics.copy()

            logger.info(f"Total CV processing completed in {total_time:.2f}s")
            cache_stats = await self.semantic_checker.get_cache_stats()
            logger.info(f"Cache stats: {cache_stats}")

            return result_data

        except Exception as e:
            import traceback

            logger.error(f"Error processing CV: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            return {
                "error": str(e),
                "traceback": traceback.format_exc(),
            }

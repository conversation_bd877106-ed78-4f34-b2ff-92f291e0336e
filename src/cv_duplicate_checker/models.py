# -*- coding: utf-8 -*-
"""Pydantic models for CV data structures"""

from typing import Any, Dict, List, Optional, TypedDict
from pydantic import BaseModel, Field


class Education(BaseModel):
    """Thông tin học vấn"""

    school: str = Field(description="Tên trường học")
    major: str = Field(description="Chuyên ngành")
    start_date: Optional[str] = Field(default=None, description="Ngày bắt đầu")
    end_date: Optional[str] = Field(default=None, description="Ngày kết thúc")
    degree: Optional[str] = Field(default=None, description="Bằng cấp")
    description: Optional[str] = Field(default=None, description="Mô tả")
    favorite_subjects: Optional[List[str]] = Field(
        default=None, description="Môn học yêu thích"
    )
    gpa: Optional[float] = Field(default=None, description="Điểm GPA")


class Experience(BaseModel):
    """Thông tin kinh nghiệm làm việc"""

    title: str = Field(description="Chức danh")
    company: str = Field(description="Tên công ty")
    start_date: str = Field(description="Ngày bắt đầu")
    description: Optional[str] = Field(default=None, description="Mô tả công việc")
    end_date: Optional[str] = Field(default=None, description="Ngày kết thúc")
    skills: Optional[List[str]] = Field(default=None, description="Kỹ năng")


class ExtraCurricular(BaseModel):
    """Thông tin hoạt động ngoại khóa"""

    role: str = Field(description="Vai trò")
    organization: str = Field(description="Tổ chức")
    start_date: str = Field(description="Ngày bắt đầu")
    end_date: Optional[str] = Field(default=None, description="Ngày kết thúc")
    description: Optional[str] = Field(default=None, description="Mô tả")
    skills: Optional[List[str]] = Field(default=None, description="Kỹ năng")


class Certification(BaseModel):
    """Thông tin chứng chỉ"""

    certification_name: str = Field(description="Tên chứng chỉ")
    organization: str = Field(description="Tổ chức cấp")
    issue_date: Optional[str] = Field(default=None, description="Ngày cấp")
    expire_date: Optional[str] = Field(default=None, description="Ngày hết hạn")
    url: Optional[str] = Field(default=None, description="Đường link")


class Language(BaseModel):
    """Thông tin ngôn ngữ"""

    language_name: str = Field(description="Tên ngôn ngữ")
    level: Optional[str] = Field(default=None, description="Trình độ")


class Achievement(BaseModel):
    """Thông tin thành tựu"""

    title: str = Field(description="Tiêu đề")
    organization: str = Field(description="Tổ chức")
    issue_date: str = Field(description="Ngày cấp")
    description: Optional[str] = Field(default=None, description="Mô tả")


class Reference(BaseModel):
    """Thông tin người tham chiếu"""

    email: str = Field(description="Email")
    name: Optional[str] = Field(default=None, description="Tên")
    position: Optional[str] = Field(default=None, description="Chức vụ")
    company: Optional[str] = Field(default=None, description="Công ty")


class ExternalDoc(BaseModel):
    """Tài liệu bên ngoài"""

    link: str = Field(description="Đường link")
    title: Optional[str] = Field(default=None, description="Tiêu đề")
    description: Optional[str] = Field(default=None, description="Mô tả")


class Summary(BaseModel):
    """Tóm tắt CV"""

    summary: str = Field(description="Nội dung tóm tắt")


class Hobby(BaseModel):
    """Sở thích"""

    hobby: str = Field(description="Sở thích")
    description: Optional[str] = Field(default=None, description="Mô tả")


class ProcessedCV(BaseModel):
    """CV đã được xử lý"""

    education: Optional[List[Education]] = Field(default=None)
    experience: Optional[List[Experience]] = Field(default=None)
    extra_curricular: Optional[List[ExtraCurricular]] = Field(default=None)
    certification: Optional[List[Certification]] = Field(default=None)
    language: Optional[List[Language]] = Field(default=None)
    achievement: Optional[List[Achievement]] = Field(default=None)
    reference: Optional[List[Reference]] = Field(default=None)
    external_doc: Optional[List[ExternalDoc]] = Field(default=None)
    summary: Optional[List[Summary]] = Field(default=None)
    hobby: Optional[List[Hobby]] = Field(default=None)


class DuplicateAnalysis(BaseModel):
    """Kết quả phân tích trùng lặp"""

    action: str = Field(description="Hành động: 'add', 'skip', 'update'")
    reason: str = Field(description="Lý do cho hành động")
    existing_index: Optional[int] = Field(
        default=None, description="Chỉ mục của item hiện tại nếu trùng"
    )
    fields_to_update: Optional[List[str]] = Field(
        default=None, description="Các trường cần cập nhật"
    )


class AgentState(TypedDict):
    """State graph for CV duplicate checker agent"""

    existing_cv: Dict[str, Any]
    new_data: Dict[str, Any]
    processed_cv: Dict[str, Any]
    current_section: Optional[str]
    analysis_results: List[Dict[str, Any]]
    final_result: Optional[Dict[str, Any]]
    # Section-specific results to avoid conflicts
    education_result: Optional[Dict[str, Any]]
    experience_result: Optional[Dict[str, Any]]
    extra_curricular_result: Optional[Dict[str, Any]]
    certification_result: Optional[Dict[str, Any]]
    language_result: Optional[Dict[str, Any]]
    achievement_result: Optional[Dict[str, Any]]
    reference_result: Optional[Dict[str, Any]]
    external_doc_result: Optional[Dict[str, Any]]
    summary_result: Optional[Dict[str, Any]]
    hobby_result: Optional[Dict[str, Any]]


class InputAgentState(TypedDict):
    """Input state for CV duplicate checker agent"""

    existing_cv: str
    new_data: str


class OutputAgentState(TypedDict):
    """Output state for CV duplicate checker agent"""

    final_result: Dict[str, Any]

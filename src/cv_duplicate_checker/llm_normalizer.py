"""
LLM-powered CV structure normalization module

This module uses OpenAI LLM to intelligently detect and normalize
structural differences between CV JSON objects, allowing the system
to handle mismatched schemas gracefully.
"""

import json
import logging
import time
from typing import Any, Dict, List, Optional, Tuple

from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import Cha<PERSON><PERSON><PERSON><PERSON><PERSON>

from .config import DEFAULT_MODEL, DEFAULT_TEMPERATURE, LLM_MAX_RETRIES, LLM_TIMEOUT
from .cache import get_cache

logger = logging.getLogger(__name__)


class LLMStructureNormalizer:
    """
    LLM-powered CV structure normalizer that intelligently maps
    different CV schemas to a consistent format
    """

    def __init__(
        self,
        llm: Optional[ChatOpenAI] = None,
        model_name: str = DEFAULT_MODEL,
        temperature: float = DEFAULT_TEMPERATURE,
    ):
        """Initialize the normalizer with LLM configuration"""
        self.llm = llm or ChatOpenAI(
            model=model_name,
            temperature=temperature,
            request_timeout=LLM_TIMEOUT,
            max_retries=LLM_MAX_RETRIES,
        )
        self._normalization_cache = {}
        self.cache = None  # Will be initialized async
        self._cache_initialized = False

    async def _ensure_cache(self):
        """Ensure cache is initialized"""
        if not self._cache_initialized:
            self.cache = await get_cache()
            self._cache_initialized = True

    def _get_target_schema_description(self) -> str:
        """Get a description of the target CV schema"""
        return """
Target CV Schema:
{
  "education": [
    {
      "school": "string (required)",
      "major": "string (required)", 
      "start_date": "string (optional)",
      "end_date": "string (optional)",
      "degree": "string (optional)",
      "description": "string (optional)",
      "favorite_subjects": ["string"] (optional),
      "gpa": "float (optional)"
    }
  ],
  "experience": [
    {
      "title": "string (required)",
      "company": "string (required)",
      "start_date": "string (required)",
      "end_date": "string (optional)",
      "description": "string (optional)",
      "skills": ["string"] (optional)
    }
  ],
  "extra_curricular": [
    {
      "role": "string (required)",
      "organization": "string (required)",
      "start_date": "string (required)",
      "end_date": "string (optional)",
      "description": "string (optional)",
      "skills": ["string"] (optional)
    }
  ],
  "certification": [
    {
      "certification_name": "string (required)",
      "organization": "string (required)",
      "issue_date": "string (optional)",
      "expire_date": "string (optional)",
      "url": "string (optional)"
    }
  ],
  "language": [
    {
      "language_name": "string (required)",
      "level": "string (optional)"
    }
  ],
  "achievement": [
    {
      "title": "string (required)",
      "organization": "string (required)",
      "issue_date": "string (required)",
      "description": "string (optional)"
    }
  ],
  "reference": [
    {
      "email": "string (required)",
      "name": "string (optional)",
      "position": "string (optional)",
      "company": "string (optional)"
    }
  ],
  "external_doc": [
    {
      "link": "string (required)",
      "title": "string (optional)",
      "description": "string (optional)"
    }
  ],
  "summary": [
    {
      "summary": "string (required)"
    }
  ],
  "hobby": [
    {
      "hobby": "string (required)",
      "description": "string (optional)"
    }
  ]
}
"""

    def _create_normalization_prompt(
        self, cv_data: Dict[str, Any], is_existing: bool = True
    ) -> List:
        """Create LLM prompt for structure normalization"""

        cv_type = "existing CV" if is_existing else "new data"

        system_prompt = f"""You are an expert CV data structure analyst. Your task is to normalize CV data to a consistent schema format.

CRITICAL INSTRUCTIONS:
1. Analyze the input CV structure and identify equivalent fields
2. Map field names to the target schema (e.g., "university" → "school", "employer" → "company")
3. Convert data types appropriately (dates to strings, single values to arrays when needed)
4. Map section names (e.g., "work_experience" → "experience", "certifications" → "certification")
5. Preserve all original data - never lose information
6. If a field doesn't map to target schema, place it in the most appropriate section
7. Return ONLY valid JSON - no explanation text

{self._get_target_schema_description()}

For each section:
- If the input has no equivalent section, omit it from output
- If input section maps to multiple target sections, split appropriately
- Ensure required fields are present or mapped from available data
- Handle missing required fields gracefully (use reasonable defaults if possible)
"""

        human_prompt = f"""Normalize this {cv_type} to the target schema:

Input CV Data:
{json.dumps(cv_data, ensure_ascii=False, indent=2)}

Return the normalized CV as valid JSON following the target schema exactly. Include only sections that have data."""

        return [
            SystemMessage(content=system_prompt),
            HumanMessage(content=human_prompt),
        ]

    async def _normalize_single_cv(
        self, cv_data: Dict[str, Any], is_existing: bool = True
    ) -> Dict[str, Any]:
        """Normalize a single CV using LLM"""
        if not cv_data:
            return {}

        # Ensure cache is initialized
        await self._ensure_cache()

        # Check Redis/memory cache first
        cached_result = await self.cache.get_normalization_result(cv_data)
        if cached_result is not None:
            logger.debug("Using cached normalization result from Redis/memory")
            return cached_result

        # Create local cache key for fallback
        cache_key = json.dumps(cv_data, sort_keys=True)
        if cache_key in self._normalization_cache:
            logger.debug("Using local cached normalization result")
            return self._normalization_cache[cache_key]

        try:
            # Create normalization prompt
            messages = self._create_normalization_prompt(cv_data, is_existing)

            # Get LLM response
            response = await self.llm.ainvoke(messages)
            normalized_text = response.content.strip()

            # Parse JSON response
            try:
                normalized_data = json.loads(normalized_text)
            except json.JSONDecodeError as e:
                logger.warning(f"LLM returned invalid JSON: {e}. Using fallback.")
                # Fallback: try to extract JSON from response
                normalized_data = self._extract_json_fallback(normalized_text)

            # Validate normalized data structure
            validated_data = self._validate_normalized_structure(normalized_data)

            # Cache successful normalization in Redis/memory
            await self.cache.set_normalization_result(cv_data, validated_data)

            # Also cache locally as fallback
            self._normalization_cache[cache_key] = validated_data

            return validated_data

        except Exception as e:
            logger.error(f"LLM normalization failed: {e}. Using fallback mapping.")
            return self._fallback_normalization(cv_data)

    def _extract_json_fallback(self, text: str) -> Dict[str, Any]:
        """Extract JSON from LLM response that might contain extra text"""
        # Try to find JSON block in the response
        start_idx = text.find("{")
        if start_idx == -1:
            return {}

        # Find the matching closing brace
        brace_count = 0
        end_idx = start_idx
        for i, char in enumerate(text[start_idx:], start_idx):
            if char == "{":
                brace_count += 1
            elif char == "}":
                brace_count -= 1
                if brace_count == 0:
                    end_idx = i + 1
                    break

        try:
            json_text = text[start_idx:end_idx]
            return json.loads(json_text)
        except json.JSONDecodeError:
            logger.warning("Failed to extract JSON from LLM response")
            return {}

    def _validate_normalized_structure(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean normalized data structure"""
        if not isinstance(data, dict):
            return {}

        # Expected sections from ProcessedCV model
        valid_sections = {
            "education",
            "experience",
            "extra_curricular",
            "certification",
            "language",
            "achievement",
            "reference",
            "external_doc",
            "summary",
            "hobby",
        }

        validated = {}
        for section, items in data.items():
            # Only include valid sections
            if section in valid_sections and isinstance(items, list):
                # Ensure all items in section are dictionaries
                validated_items = []
                for item in items:
                    if isinstance(item, dict):
                        validated_items.append(item)

                if validated_items:  # Only add section if it has valid items
                    validated[section] = validated_items

        return validated

    def _fallback_normalization(self, cv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback normalization using simple field mapping rules"""
        logger.info("Using fallback normalization rules")

        # Basic field mapping rules
        section_mappings = {
            "work_experience": "experience",
            "job_experience": "experience",
            "employment": "experience",
            "jobs": "experience",
            "educations": "education",
            "schools": "education",
            "academic": "education",
            "certifications": "certification",
            "certificates": "certification",
            "languages": "language",
            "skills": "language",  # Sometimes language skills are in skills
            "achievements": "achievement",
            "awards": "achievement",
            "references": "reference",
            "contacts": "reference",
            "hobbies": "hobby",
            "interests": "hobby",
            "extra_curriculars": "extra_curricular",
            "activities": "extra_curricular",
            "external_docs": "external_doc",
            "documents": "external_doc",
            "links": "external_doc",
        }

        field_mappings = {
            # Education mappings
            "university": "school",
            "college": "school",
            "institution": "school",
            "field_of_study": "major",
            "study_field": "major",
            "specialization": "major",
            # Experience mappings
            "position": "title",
            "job_title": "title",
            "role": "title",
            "employer": "company",
            "organization": "company",
            "workplace": "company",
            # Common field mappings
            "begin_date": "start_date",
            "from_date": "start_date",
            "finish_date": "end_date",
            "to_date": "end_date",
            "until_date": "end_date",
        }

        normalized = {}

        for original_section, items in cv_data.items():
            if not isinstance(items, list):
                continue

            # Map section name
            target_section = section_mappings.get(original_section, original_section)

            normalized_items = []
            for item in items:
                if not isinstance(item, dict):
                    continue

                normalized_item = {}
                for field, value in item.items():
                    # Map field name
                    target_field = field_mappings.get(field, field)
                    normalized_item[target_field] = value

                normalized_items.append(normalized_item)

            if normalized_items:
                normalized[target_section] = normalized_items

        return normalized

    async def normalize_cv_pair(
        self, existing_cv: Dict[str, Any], new_data: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Normalize both CVs to consistent structure

        Args:
            existing_cv: Original CV data
            new_data: New CV data to be merged

        Returns:
            Tuple of (normalized_existing_cv, normalized_new_data)
        """
        start_time = time.time()

        logger.info("Starting LLM-powered CV structure normalization...")

        try:
            # Normalize both CVs concurrently for better performance
            import asyncio

            normalized_existing, normalized_new = await asyncio.gather(
                self._normalize_single_cv(existing_cv, is_existing=True),
                self._normalize_single_cv(new_data, is_existing=False),
            )

            processing_time = time.time() - start_time
            logger.info(f"CV normalization completed in {processing_time:.2f}s")

            return normalized_existing, normalized_new

        except Exception as e:
            logger.error(f"CV normalization failed: {e}")
            # Return original data as fallback
            return existing_cv, new_data

    def get_normalization_stats(self) -> Dict[str, Any]:
        """Get normalization performance statistics"""
        return {
            "cache_size": len(self._normalization_cache),
            "cached_normalizations": len(self._normalization_cache),
        }

    def clear_cache(self):
        """Clear normalization cache"""
        self._normalization_cache.clear()
        logger.info("Normalization cache cleared")

"""Configuration constants and settings for CV Duplicate Checker"""

import os

from dotenv import load_dotenv

load_dotenv()

# API Configuration
DEFAULT_HOST = "127.0.0.1"
DEFAULT_PORT = 8000
DEFAULT_LOG_LEVEL = "info"

# Agent Configuration
LLM_TIMEOUT = int(os.getenv("LLM_TIMEOUT", "30"))
LLM_MAX_RETRIES = int(os.getenv("LLM_MAX_RETRIES", "2"))
DEFAULT_MODEL = "gpt-4o-mini"
DEFAULT_TEMPERATURE = 0.0

# Memory Management
MAX_ACTIONS_PER_SECTION = int(os.getenv("MAX_ACTIONS_PER_SECTION", "100"))
MAX_TOTAL_ACTIONS = int(os.getenv("MAX_TOTAL_ACTIONS", "1000"))

# Security Configuration
ALLOWED_ORIGINS = os.getenv(
    "ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8000"
).split(",")
MAX_REQUEST_SIZE = int(os.getenv("MAX_REQUEST_SIZE", "10485760"))

# Semantic Matching Configuration
SEMANTIC_CACHE_SIZE = int(os.getenv("SEMANTIC_CACHE_SIZE", "1000"))
SEMANTIC_CACHE_TTL = int(os.getenv("SEMANTIC_CACHE_TTL", "3600"))

# Redis Configuration
REDIS_ENABLED = os.getenv("REDIS_ENABLED", "true").lower() == "true"
REDIS_URL = os.getenv("REDIS_URL", "")
REDIS_HOST = os.getenv("REDIS_HOST", "redis")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "")
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_TTL = int(os.getenv("REDIS_TTL", "3600"))  # 1 hour default
REDIS_MAX_CONNECTIONS = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
REDIS_SOCKET_CONNECT_TIMEOUT = int(os.getenv("REDIS_SOCKET_CONNECT_TIMEOUT", "5"))
REDIS_SOCKET_KEEPALIVE = os.getenv("REDIS_SOCKET_KEEPALIVE", "true").lower() == "true"
REDIS_SOCKET_KEEPALIVE_OPTIONS = {}
REDIS_RETRY_ON_TIMEOUT = os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true"
REDIS_HEALTH_CHECK_INTERVAL = int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30"))
REDIS_MAX_MEMORY_POLICY = os.getenv("REDIS_MAX_MEMORY_POLICY", "allkeys-lru")
REDIS_KEY_PREFIX = os.getenv("REDIS_KEY_PREFIX", "cv-dup-checker")

# Batch Processing Configuration
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "5"))
MAX_CONCURRENT_BATCHES = int(os.getenv("MAX_CONCURRENT_BATCHES", "3"))
BATCH_TIMEOUT = int(os.getenv("BATCH_TIMEOUT", "60"))

# Performance Optimization
ENABLE_SEMANTIC_CACHE = os.getenv("ENABLE_SEMANTIC_CACHE", "true").lower() == "true"
ENABLE_BATCH_PROCESSING = os.getenv("ENABLE_BATCH_PROCESSING", "true").lower() == "true"
ENABLE_EARLY_EXIT = os.getenv("ENABLE_EARLY_EXIT", "true").lower() == "true"

# CV Section Configuration
CV_SECTIONS = [
    "education",
    "experience",
    "extra_curricular",
    "certification",
    "language",
    "achievement",
    "reference",
    "external_doc",
    "summary",
    "hobby",
]

# Duplicate Criteria Configuration
DUPLICATE_CRITERIA = {
    "education": ["school", "major", "start_date"],
    "experience": ["title", "company", "start_date"],
    "extra_curricular": ["role", "organization", "start_date"],
    "certification": ["certification_name", "organization"],
    "language": ["language_name"],
    "achievement": ["title", "organization", "issue_date"],
    "reference": ["email"],
    "external_doc": ["link"],
    "summary": ["summary"],
    "hobby": ["hobby"],
}

# Updatable Fields Configuration
UPDATABLE_FIELDS = {
    "education": ["description", "end_date", "degree", "gpa", "favorite_subjects"],
    "experience": ["description", "end_date", "skills"],
    "extra_curricular": ["description", "end_date"],
    "certification": ["issue_date", "expire_date", "url"],
    "language": ["level"],
    "achievement": ["description"],
    "reference": ["name", "position", "company", "phone"],
    "external_doc": ["title", "description"],
    "summary": [],
    "hobby": ["description"],
}

# Field Type Mapping for Semantic Matching
FIELD_TYPE_MAPPING = {
    "school": "school",
    "university": "school",
    "company": "company",
    "organization": "company",
    "major": "major",
    "field_of_study": "major",
    "title": "position",
    "position": "position",
    "role": "position",
    "certification_name": "certification",
}

# Error Messages
ERROR_MESSAGES = {
    "agent_init_failed": "Failed to initialize CV Duplicate Checker Agent",
    "invalid_input": "Invalid CV data provided",
    "processing_failed": "Failed to process CV data",
    "timeout_error": "Request timeout - processing took too long",
    "rate_limit_exceeded": "Rate limit exceeded - please try again later",
    "service_unavailable": "Service temporarily unavailable",
}

# Langfuse Configuration
LANGFUSE_ENABLED = os.getenv("LANGFUSE_ENABLED", "false").lower() == "true"
LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY", "")
LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY", "")
LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com")
LANGFUSE_DEBUG = os.getenv("LANGFUSE_DEBUG", "false").lower() == "true"

# Sentry Configuration
SENTRY_DSN = os.getenv("SENTRY_DSN", "")
SENTRY_ENABLED = os.getenv("SENTRY_ENABLED", "false").lower() == "true"
SENTRY_SAMPLE_RATE = float(os.getenv("SENTRY_SAMPLE_RATE", "1.0"))
SENTRY_TRACES_SAMPLE_RATE = float(os.getenv("SENTRY_TRACES_SAMPLE_RATE", "0.1"))

# Logging Configuration
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"


def get_environment() -> str:
    """Get current environment (development, staging, production)"""
    return os.getenv("ENVIRONMENT", "development").lower()


def is_production() -> bool:
    """Check if running in production environment"""
    return get_environment() == "production"


def is_development() -> bool:
    """Check if running in development environment"""
    return get_environment() == "development"


def get_debug_mode() -> bool:
    """Get debug mode setting"""
    return os.getenv("DEBUG", "false").lower() == "true" and not is_production()

# Development Docker Compose Configuration
# This extends the main docker-compose.yml with development-specific settings
#
# Usage:
#   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

version: "3.8"

services:
  cv-duplicate-checker:
    environment:
      # Development settings
      - API_RELOAD=true
      - API_LOG_LEVEL=debug
      - DEBUG=true
      - SENTRY_ENVIRONMENT=development
      # Redis development settings
      - REDIS_ENABLED=true
      - REDIS_TTL=1800  # Shorter TTL for development
    volumes:
      # Mount source code for live reload
      - ./src:/app/src:ro
      - ./templates:/app/templates:ro
      - ./static:/app/static:ro
      - ./main.py:/app/main.py:ro
      - ./pyproject.toml:/app/pyproject.toml:ro
    command: ["python", "main.py", "--reload", "--log-level", "debug"]
    # Override health check for faster development feedback
    healthcheck:
      interval: 10s
      timeout: 5s
      retries: 2
      start_period: 10s

  redis:
    # Development Redis settings
    command: >
      sh -c "
      redis-server --maxmemory 128mb --maxmemory-policy allkeys-lru --save \"\"
      "
    # Faster health checks for development
    healthcheck:
      interval: 5s
      timeout: 3s
      retries: 2
      start_period: 5s

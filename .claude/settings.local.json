{"includeCoAuthoredBy": false, "permissions": {"allow": ["<PERSON><PERSON>(source:*)", "Bash(python test_agent.py:*)", "Bash(ls:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "<PERSON><PERSON>(uv run:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(uv sync:*)", "Bash(rg:*)", "<PERSON><PERSON>(touch:*)", "Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(cat:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(npx @mermaid-js/mermaid-cli mmdc:*)", "Bash(npx @mermaid-js/mermaid-cli validate:*)", "Bash(git push:*)", "Bash(ruff check:*)", "Bash(ruff format:*)", "WebFetch(domain:langfuse.com)"], "deny": []}}
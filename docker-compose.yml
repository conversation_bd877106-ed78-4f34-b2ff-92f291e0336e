version: "3.8"

services:
  cv-duplicate-checker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cv-duplicate-checker
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      # Required: OpenAI API key
      - OPENAI_API_KEY=${OPENAI_API_KEY}

      # Optional: OpenAI configuration
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4o-mini}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0}

      # API Server configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_LOG_LEVEL=${API_LOG_LEVEL:-info}
      - API_RELOAD=${API_RELOAD:-false}

      # Optional: Debug mode
      - DEBUG=${DEBUG:-false}

      # Redis Configuration
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_DB=${REDIS_DB:-0}
      - REDIS_TTL=${REDIS_TTL:-3600}
      - REDIS_MAX_CONNECTIONS=${REDIS_MAX_CONNECTIONS:-10}

      # Sentry Configuration
      - SENTRY_DSN=${SENTRY_DSN}
      - SENTRY_ENABLED=${SENTRY_ENABLED:-false}
      - SENTRY_ENVIRONMENT=${SENTRY_ENVIRONMENT:-production}
      - SENTRY_SAMPLE_RATE=${SENTRY_SAMPLE_RATE:-1.0}
      - SENTRY_TRACES_SAMPLE_RATE=${SENTRY_TRACES_SAMPLE_RATE:-0.1}

      # Langfuse
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_HOST=${LANGFUSE_HOST:-https://langfuse.xstaging.navigosgroup.site}
    volumes:
      # Development: Mount source code for live reload (uncomment for dev)
      # - ./src:/app/src:ro
      # - ./templates:/app/templates:ro
      # - ./static:/app/static:ro

      # Logs volume
      - cv-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - cv-network
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    container_name: cv-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    command: >
      sh -c "
      if [ -n \"$$REDIS_PASSWORD\" ]; then
        redis-server --requirepass $$REDIS_PASSWORD --maxmemory 256mb --maxmemory-policy allkeys-lru
      else
        redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
      fi
      "
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - cv-network

  # Optional: Development override service
  cv-duplicate-checker-dev:
    extends: cv-duplicate-checker
    profiles: ["dev"]
    environment:
      - API_RELOAD=true
      - API_LOG_LEVEL=debug
      - DEBUG=true
    volumes:
      # Development: Live reload with source mounting
      - ./src:/app/src:ro
      - ./templates:/app/templates:ro
      - ./static:/app/static:ro
      - ./main.py:/app/main.py:ro
      - cv-logs:/app/logs
    command: ["python", "main.py", "--reload", "--log-level", "debug"]

volumes:
  cv-logs:
    driver: local
  redis-data:
    driver: local

networks:
  cv-network:
    driver: bridge

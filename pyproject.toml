[project]
name = "upzi-cv-duplicate-checker"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "langchain>=0.3.25",
    "langchain-openai>=0.3.21",
    "langgraph>=0.4.8",
    "python-dotenv>=1.1.0",
    "fastapi>=0.115.0",
    "uvicorn[standard]>=0.32.0",
    "requests>=2.32.0",
    "jinja2>=3.1.0",
    "python-multipart>=0.0.6",
    "langfuse>=3.0.0",
    "sentry-sdk[fastapi]>=2.0.0",
    "loguru>=0.7.3",
    "redis[hiredis]>=5.0.0",
]

[dependency-groups]
dev = [
    "matplotlib>=3.10.3",
    "memory-profiler>=0.61.0",
    "objgraph>=3.6.2",
    "pre-commit>=4.2.0",
    "psutil>=7.0.0",
    "pympler>=1.1",
]

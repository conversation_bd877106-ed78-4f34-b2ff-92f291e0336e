#!/usr/bin/env python3
"""
Test Redis downtime resilience for CV Duplicate Checker

This test verifies that the API and agent continue working seamlessly
when Redis is unavailable or becomes unavailable during operation.
"""

import asyncio
import json
import os
import sys
import time

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from cv_duplicate_checker import CVDuplicateCheckerAgent
from cv_duplicate_checker.cache import get_cache


async def test_agent_with_redis_disabled():
    """Test that agent works normally when <PERSON><PERSON> is disabled"""
    print("=== Testing Agent with Redis Disabled ===")

    try:
        # Temporarily disable Redis
        original_redis_enabled = os.environ.get("REDIS_ENABLED", "true")
        os.environ["REDIS_ENABLED"] = "false"

        # Create agent (should use memory cache only)
        agent = CVDuplicateCheckerAgent()

        # Test data
        existing_cv = {
            "education": [
                {"school": "MIT", "major": "Computer Science", "start_date": "2020"}
            ]
        }

        new_data = {
            "education": [
                {
                    "school": "Massachusetts Institute of Technology",
                    "major": "Computer Science",
                    "start_date": "2020",
                    "degree": "Bachelor",
                }
            ]
        }

        # Process CV data
        start_time = time.time()
        result = await agent.process(
            json.dumps(existing_cv, ensure_ascii=False),
            json.dumps(new_data, ensure_ascii=False),
        )
        processing_time = time.time() - start_time

        # Verify result
        assert result is not None, "Agent should return valid result"
        assert "action_summary" in result, "Result should contain action summary"
        assert result["action_summary"]["total_actions"] > 0, "Should process actions"

        print(f"✅ Agent processed successfully in {processing_time:.2f}s")
        print(f"  Actions: {result['action_summary']['total_actions']}")
        print(f"  Added: {result['action_summary']['added']}")
        print(f"  Updated: {result['action_summary']['updated']}")

        # Restore original setting
        os.environ["REDIS_ENABLED"] = original_redis_enabled

        return True

    except Exception as e:
        # Restore original setting
        os.environ["REDIS_ENABLED"] = original_redis_enabled
        print(f"❌ Agent failed with Redis disabled: {e}")
        return False


async def test_agent_with_redis_unavailable():
    """Test agent resilience when Redis host is unavailable"""
    print("=== Testing Agent with Redis Unavailable ===")

    try:
        # Set Redis to non-existent host
        original_redis_host = os.environ.get("REDIS_HOST", "redis")
        os.environ["REDIS_HOST"] = "nonexistent-redis-host"
        os.environ["REDIS_ENABLED"] = "true"

        # Create agent (should fail to connect to Redis but continue with memory cache)
        agent = CVDuplicateCheckerAgent()

        # Test data
        existing_cv = {
            "education": [{"school": "DHBK", "major": "AI", "start_date": "2021"}]
        }

        new_data = {
            "education": [
                {
                    "school": "Đại học Bách Khoa",
                    "major": "Artificial Intelligence",
                    "start_date": "2021",
                    "gpa": 3.8,
                }
            ]
        }

        # Process CV data
        start_time = time.time()
        result = await agent.process(
            json.dumps(existing_cv, ensure_ascii=False),
            json.dumps(new_data, ensure_ascii=False),
        )
        processing_time = time.time() - start_time

        # Verify result
        assert result is not None, "Agent should return valid result"
        assert "action_summary" in result, "Result should contain action summary"
        assert result["action_summary"]["total_actions"] > 0, "Should process actions"

        # Verify cache is working (may be Redis or memory fallback)
        cache = await get_cache()
        cache_info = await cache.get_cache_info()
        # Cache should work regardless of Redis health
        print(f"  Redis healthy: {cache_info['redis_healthy']}")
        print(f"  Memory cache size: {cache_info['memory_cache_size']}")

        # The key test is that the agent worked successfully

        print(
            f"✅ Agent handled potential Redis unavailability gracefully in {processing_time:.2f}s"
        )
        print(f"  Actions: {result['action_summary']['total_actions']}")

        # Restore original setting
        os.environ["REDIS_HOST"] = original_redis_host

        return True

    except Exception as e:
        # Restore original setting
        os.environ["REDIS_HOST"] = original_redis_host
        print(f"❌ Agent failed with Redis unavailable: {e}")
        return False


async def test_cache_operations_with_redis_down():
    """Test direct cache operations when Redis is down"""
    print("=== Testing Cache Operations with Redis Down ===")

    try:
        # Set Redis to non-existent host
        original_redis_host = os.environ.get("REDIS_HOST", "redis")
        os.environ["REDIS_HOST"] = "nonexistent-redis-host-2"
        os.environ["REDIS_ENABLED"] = "true"

        cache = await get_cache()

        # Test basic cache operations
        test_key = "test:downtime:key"
        test_value = {"test": "value", "number": 42}

        # Set operation (should succeed with memory cache)
        set_result = await cache.set(test_key, test_value)
        print(f"  Set operation result: {set_result}")

        # Get operation (should succeed with memory cache)
        get_result = await cache.get(test_key)
        assert get_result == test_value, "Should retrieve from memory cache"
        print(f"  Get operation successful: {get_result}")

        # Semantic similarity operations
        sim_result = await cache.set_semantic_similarity(
            "Google", "Google LLC", "company", True
        )
        print(f"  Semantic set result: {sim_result}")

        cached_sim = await cache.get_semantic_similarity(
            "Google", "Google LLC", "company"
        )
        assert cached_sim is True, "Should retrieve semantic similarity from memory"
        print(f"  Semantic get successful: {cached_sim}")

        # Batch operations
        batch_comparisons = [
            (("Apple", "Apple Inc", "company"), True),
            (("Microsoft", "Microsoft Corporation", "company"), True),
        ]
        batch_result = await cache.set_multiple_semantic_similarities(batch_comparisons)
        print(f"  Batch set result: {batch_result}")

        # Get cache info (should work even without Redis)
        cache_info = await cache.get_cache_info()
        # The key test is that cache operations work regardless of Redis health
        assert cache_info["memory_cache_size"] > 0, "Memory cache should have data"
        print("  Cache info retrieved successfully")
        print(f"  Redis healthy: {cache_info['redis_healthy']}")
        print(f"  Memory cache entries: {cache_info['memory_cache_size']}")

        # Clear cache (should work with memory cache)
        clear_result = await cache.clear_cache()
        print(f"  Clear cache result: {clear_result}")

        # Verify memory cache is cleared
        final_cache_info = await cache.get_cache_info()
        assert (
            final_cache_info["memory_cache_size"] == 0
        ), "Memory cache should be empty"

        print("✅ All cache operations succeeded with memory fallback")

        # Restore original setting
        os.environ["REDIS_HOST"] = original_redis_host

        return True

    except Exception as e:
        # Restore original setting
        os.environ["REDIS_HOST"] = original_redis_host
        print(f"❌ Cache operations failed with Redis down: {e}")
        return False


async def test_performance_degradation_gracefully():
    """Test that performance degrades gracefully when Redis is unavailable"""
    print("=== Testing Performance Degradation with Redis Down ===")

    try:
        # Test with Redis working first
        os.environ["REDIS_HOST"] = "localhost"  # Assume Redis might be running locally
        os.environ["REDIS_ENABLED"] = "true"

        agent = CVDuplicateCheckerAgent()

        test_data = {
            "existing_cv": {
                "education": [
                    {"school": "Stanford", "major": "CS", "start_date": "2020"},
                    {"school": "Berkeley", "major": "EE", "start_date": "2019"},
                ]
            },
            "new_data": {
                "education": [
                    {
                        "school": "Stanford University",
                        "major": "Computer Science",
                        "start_date": "2020",
                        "gpa": 3.9,
                    },
                    {
                        "school": "UC Berkeley",
                        "major": "Electrical Engineering",
                        "start_date": "2019",
                        "degree": "MS",
                    },
                ]
            },
        }

        # First run (may or may not have Redis)
        start_time = time.time()
        result1 = await agent.process(
            json.dumps(test_data["existing_cv"], ensure_ascii=False),
            json.dumps(test_data["new_data"], ensure_ascii=False),
        )
        time1 = time.time() - start_time

        # Now force Redis to be unavailable
        os.environ["REDIS_HOST"] = "definitely-nonexistent-redis-host"

        # Create new agent instance (to reset cache connections)
        agent2 = CVDuplicateCheckerAgent()

        # Second run (definitely without Redis)
        start_time = time.time()
        result2 = await agent2.process(
            json.dumps(test_data["existing_cv"], ensure_ascii=False),
            json.dumps(test_data["new_data"], ensure_ascii=False),
        )
        time2 = time.time() - start_time

        # Verify both results are valid and consistent
        assert (
            result1 is not None and result2 is not None
        ), "Both results should be valid"
        assert (
            result1["action_summary"]["total_actions"]
            == result2["action_summary"]["total_actions"]
        ), "Results should be consistent"

        print("✅ Performance degradation handled gracefully")
        print(f"  Run 1 (Redis may be available): {time1:.2f}s")
        print(f"  Run 2 (Redis unavailable): {time2:.2f}s")
        print(
            f"  Results consistent: {result1['action_summary'] == result2['action_summary']}"
        )

        return True

    except Exception as e:
        print(f"❌ Performance degradation test failed: {e}")
        return False


async def main():
    """Run all Redis downtime resilience tests"""
    print("🔧 Testing Redis Downtime Resilience for CV Duplicate Checker\n")

    tests = [
        ("Agent with Redis Disabled", test_agent_with_redis_disabled),
        ("Agent with Redis Unavailable", test_agent_with_redis_unavailable),
        ("Cache Operations with Redis Down", test_cache_operations_with_redis_down),
        ("Performance Degradation Gracefully", test_performance_degradation_gracefully),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        try:
            success = await test_func()
            if success:
                print(f"✅ {test_name} - PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} - FAILED")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
            failed += 1
        print("-" * 60)

    print(f"\n{'=' * 60}")
    print("REDIS DOWNTIME RESILIENCE TESTS SUMMARY")
    print(f"Total: {len(tests)} | Passed: {passed} | Failed: {failed}")
    print(f"{'=' * 60}")

    if passed == len(tests):
        print("🎉 All Redis downtime resilience tests passed!")
        print("💪 API and Agent are fully resilient to Redis downtime!")
    else:
        print(f"⚠️  {failed} test(s) failed. Redis downtime may affect functionality.")

    # Clean up
    try:
        from cv_duplicate_checker.cache import close_cache

        await close_cache()
        print("\n🧹 Cache connections closed successfully")
    except Exception as e:
        print(f"\n⚠️  Error closing cache connections: {e}")


if __name__ == "__main__":
    asyncio.run(main())

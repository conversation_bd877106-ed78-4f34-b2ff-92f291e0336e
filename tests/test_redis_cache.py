#!/usr/bin/env python3
"""
Test Redis caching functionality of CV Duplicate Checker Agent

This test verifies that the Redis cache system works correctly for semantic
similarity matching and provides proper fallback to in-memory cache.
"""

import asyncio
import json
import os
import sys
import time

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from cv_duplicate_checker import CVDuplicateCheckerAgent
from cv_duplicate_checker.cache import get_cache


async def test_cache_initialization():
    """Test cache initialization and basic connectivity"""
    print("=== Testing Cache Initialization ===")

    try:
        cache = await get_cache()
        assert cache is not None, "Cache should not be None"

        # Test cache info retrieval
        cache_info = await cache.get_cache_info()
        assert "redis_enabled" in cache_info, "Cache info should contain redis_enabled"
        assert (
            "memory_cache_size" in cache_info
        ), "Cache info should contain memory_cache_size"
        assert "stats" in cache_info, "Cache info should contain stats"

        print("✅ Cache initialized successfully")
        print(f"  Redis enabled: {cache_info['redis_enabled']}")
        print(f"  Redis healthy: {cache_info['redis_healthy']}")
        print(f"  Memory cache size: {cache_info['memory_cache_size']}")

        return True

    except Exception as e:
        print(f"❌ Cache initialization failed: {e}")
        return False


async def test_cache_basic_operations():
    """Test basic cache set/get operations"""
    print("=== Testing Basic Cache Operations ===")

    try:
        cache = await get_cache()

        # Test basic set/get
        test_key = "test:cache:key"
        test_value = {"result": True, "confidence": 0.95}

        # Set value
        set_result = await cache.set(test_key, test_value)
        print(f"  Set operation result: {set_result}")

        # Get value
        retrieved_value = await cache.get(test_key)
        assert (
            retrieved_value == test_value
        ), f"Retrieved value {retrieved_value} != expected {test_value}"

        # Test semantic similarity cache
        similarity_result = await cache.set_semantic_similarity(
            "DHBK", "Đại học Bách Khoa", "school", True
        )
        print(f"  Semantic similarity set result: {similarity_result}")

        cached_similarity = await cache.get_semantic_similarity(
            "DHBK", "Đại học Bách Khoa", "school"
        )
        assert (
            cached_similarity is True
        ), f"Cached similarity {cached_similarity} != expected True"

        print("✅ Basic cache operations successful")
        return True

    except Exception as e:
        print(f"❌ Basic cache operations failed: {e}")
        return False


async def test_cache_batch_operations():
    """Test batch cache operations for multiple semantic similarities"""
    print("=== Testing Batch Cache Operations ===")

    try:
        cache = await get_cache()

        # Test batch get
        test_comparisons = [
            ("MIT", "Massachusetts Institute of Technology", "school"),
            ("Google", "Google LLC", "company"),
            ("AI", "Artificial Intelligence", "major"),
            (
                "New Company",
                "Another Company",
                "company",
            ),  # This won't be cached initially
        ]

        # First, set some values
        cache_operations = [
            (("MIT", "Massachusetts Institute of Technology", "school"), True),
            (("Google", "Google LLC", "company"), True),
            (("AI", "Artificial Intelligence", "major"), True),
        ]

        batch_set_result = await cache.set_multiple_semantic_similarities(
            cache_operations
        )
        print(f"  Batch set result: {batch_set_result}")

        # Now get them in batch
        batch_results = await cache.get_multiple_semantic_similarities(test_comparisons)

        print("  Batch get results:")
        for comparison, result in batch_results.items():
            value1, value2, field_type = comparison
            print(f"    {value1} ≈ {value2} ({field_type}): {result}")

        # Check that we got the expected results
        assert (
            batch_results[("MIT", "Massachusetts Institute of Technology", "school")]
            is True
        )
        assert batch_results[("Google", "Google LLC", "company")] is True
        assert batch_results[("AI", "Artificial Intelligence", "major")] is True
        assert (
            batch_results[("New Company", "Another Company", "company")] is None
        )  # Not cached

        print("✅ Batch cache operations successful")
        return True

    except Exception as e:
        print(f"❌ Batch cache operations failed: {e}")
        return False


async def test_cache_performance_with_agent():
    """Test cache performance when using the agent"""
    print("=== Testing Cache Performance with Agent ===")

    try:
        agent = CVDuplicateCheckerAgent()

        # Clear cache first
        cache = await get_cache()
        await cache.clear_cache()

        # Get initial cache stats
        initial_stats = await cache.get_cache_info()
        initial_cache_stats = initial_stats["stats"]

        print(f"  Initial cache stats: {initial_cache_stats}")

        # Run a test that should populate the cache
        existing_cv = {
            "education": [
                {"school": "DHBK", "major": "Computer Science", "start_date": "2020"},
                {"school": "MIT", "major": "AI", "start_date": "2018"},
            ]
        }

        new_data = {
            "education": [
                {
                    "school": "Đại học Bách Khoa",
                    "major": "Computer Science",
                    "start_date": "2020",
                    "degree": "Bachelor",
                },
                {
                    "school": "Massachusetts Institute of Technology",
                    "major": "Artificial Intelligence",
                    "start_date": "2018",
                    "end_date": "2022",
                },
            ]
        }

        # First run - populate cache
        start_time = time.time()
        result1 = await agent.process(
            json.dumps(existing_cv, ensure_ascii=False),
            json.dumps(new_data, ensure_ascii=False),
        )
        first_run_time = time.time() - start_time

        # Get cache stats after first run
        after_first_stats = await cache.get_cache_info()
        after_first_cache_stats = after_first_stats["stats"]

        print(f"  Cache stats after first run: {after_first_cache_stats}")
        print(f"  First run time: {first_run_time:.2f}s")

        # Second run - should hit cache
        start_time = time.time()
        result2 = await agent.process(
            json.dumps(existing_cv, ensure_ascii=False),
            json.dumps(new_data, ensure_ascii=False),
        )
        second_run_time = time.time() - start_time

        # Get final cache stats
        final_stats = await cache.get_cache_info()
        final_cache_stats = final_stats["stats"]

        print(f"  Cache stats after second run: {final_cache_stats}")
        print(f"  Second run time: {second_run_time:.2f}s")

        # Verify cache hits increased
        cache_hits_increased = (
            final_cache_stats.get("redis_hits", 0)
            + final_cache_stats.get("memory_hits", 0)
        ) > (
            after_first_cache_stats.get("redis_hits", 0)
            + after_first_cache_stats.get("memory_hits", 0)
        )

        print(f"  Cache hits increased: {cache_hits_increased}")

        # Verify results are consistent
        assert (
            result1["action_summary"] == result2["action_summary"]
        ), "Results should be consistent"

        print("✅ Cache performance test successful")
        return True

    except Exception as e:
        print(f"❌ Cache performance test failed: {e}")
        return False


async def test_cache_fallback():
    """Test cache fallback behavior when Redis is unavailable"""
    print("=== Testing Cache Fallback Behavior ===")

    try:
        cache = await get_cache()

        # Test direct memory cache functionality
        # Force use memory cache by testing when Redis might be unavailable

        # Test semantic similarity with memory cache
        test_value1 = "Test Company"
        test_value2 = "Test Company Inc"
        field_type = "company"

        # Set in cache (should work with memory fallback)
        set_result = await cache.set_semantic_similarity(
            test_value1, test_value2, field_type, True
        )
        print(f"  Fallback set result: {set_result}")

        # Get from cache (should work with memory fallback)
        get_result = await cache.get_semantic_similarity(
            test_value1, test_value2, field_type
        )
        assert get_result is True, f"Fallback get result {get_result} != expected True"

        # Test cache info is still available
        cache_info = await cache.get_cache_info()
        assert (
            cache_info is not None
        ), "Cache info should be available even during fallback"

        print("✅ Cache fallback test successful")
        return True

    except Exception as e:
        print(f"❌ Cache fallback test failed: {e}")
        return False


async def test_cache_clear():
    """Test cache clearing functionality"""
    print("=== Testing Cache Clear Functionality ===")

    try:
        cache = await get_cache()

        # Add some test data
        await cache.set_semantic_similarity("Test1", "Test2", "general", True)
        await cache.set("test:key", {"value": "test"})

        # Verify data exists
        cached_similarity = await cache.get_semantic_similarity(
            "Test1", "Test2", "general"
        )
        cached_value = await cache.get("test:key")

        assert cached_similarity is True, "Test data should exist before clear"
        assert cached_value is not None, "Test data should exist before clear"

        # Clear cache
        clear_result = await cache.clear_cache()
        print(f"  Clear result: {clear_result}")

        # Verify data is cleared (at least from memory cache)
        cache_info = await cache.get_cache_info()
        memory_cache_size = cache_info["memory_cache_size"]

        print(f"  Memory cache size after clear: {memory_cache_size}")

        print("✅ Cache clear test successful")
        return True

    except Exception as e:
        print(f"❌ Cache clear test failed: {e}")
        return False


async def test_cache_key_generation():
    """Test cache key generation and normalization"""
    print("=== Testing Cache Key Generation ===")

    try:
        cache = await get_cache()

        # Test that different orders produce same cache key
        value1_a = "  MIT  "  # With spaces
        value1_b = "mit"  # Different case
        value2_a = "  Massachusetts Institute of Technology  "
        value2_b = "massachusetts institute of technology"

        # Set with first variation
        await cache.set_semantic_similarity(value1_a, value2_a, "school", True)

        # Get with second variation (should hit cache due to normalization)
        result = await cache.get_semantic_similarity(value1_b, value2_b, "school")

        # Should get cached result due to key normalization
        print(f"  Cached result from normalized key: {result}")

        # Test order independence
        result1 = await cache.get_semantic_similarity("A", "B", "general")
        result2 = await cache.get_semantic_similarity("B", "A", "general")

        # Both should return same result (None in this case since not cached)
        assert result1 == result2, "Cache should be order-independent"

        print("✅ Cache key generation test successful")
        return True

    except Exception as e:
        print(f"❌ Cache key generation test failed: {e}")
        return False


async def main():
    """Run all Redis cache tests"""
    print("🔧 Testing Redis Cache System for CV Duplicate Checker\n")

    tests = [
        ("Cache Initialization", test_cache_initialization),
        ("Basic Cache Operations", test_cache_basic_operations),
        ("Batch Cache Operations", test_cache_batch_operations),
        ("Cache Performance with Agent", test_cache_performance_with_agent),
        ("Cache Fallback Behavior", test_cache_fallback),
        ("Cache Clear Functionality", test_cache_clear),
        ("Cache Key Generation", test_cache_key_generation),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        try:
            success = await test_func()
            if success:
                print(f"✅ {test_name} - PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} - FAILED")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
            failed += 1
        print("-" * 60)

    print(f"\n{'=' * 60}")
    print("REDIS CACHE TESTS SUMMARY")
    print(f"Total: {len(tests)} | Passed: {passed} | Failed: {failed}")
    print(f"{'=' * 60}")

    if passed == len(tests):
        print("🎉 All Redis cache tests passed!")
    else:
        print(f"⚠️  {failed} test(s) failed. Check Redis connection and configuration.")

    # Clean up
    try:
        from cv_duplicate_checker.cache import close_cache

        await close_cache()
        print("\n🧹 Cache connections closed successfully")
    except Exception as e:
        print(f"\n⚠️  Error closing cache connections: {e}")


if __name__ == "__main__":
    asyncio.run(main())

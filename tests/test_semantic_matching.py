#!/usr/bin/env python3
"""
Test semantic matching functionality of CV Duplicate Checker Agent

This test verifies that the LLM can correctly identify semantic similarities
between different representations of the same entity.
"""

import asyncio
import json
import os
import sys

# Add src to path to import the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))

from cv_duplicate_checker import CVDuplicateCheckerAgent


async def test_school_semantic_matching():
    """Test semantic matching for schools/universities"""
    print("=== Testing School Semantic Matching ===")

    agent = CVDuplicateCheckerAgent()

    existing_cv = {
        "education": [
            {"school": "DHBK", "major": "<PERSON><PERSON><PERSON> học m<PERSON> t<PERSON>", "start_date": "2020"},
            {"school": "MIT", "major": "Computer Science", "start_date": "2018"},
            {
                "school": "HUST",
                "major": "Electronics Engineering",
                "start_date": "2019",
            },
        ]
    }

    new_data = {
        "education": [
            # Should match with DHBK
            {
                "school": "<PERSON><PERSON><PERSON>",
                "major": "<PERSON><PERSON><PERSON> họ<PERSON> m<PERSON>",
                "start_date": "2020",
                "degree": "Bachelor",
                "gpa": 3.5,
            },
            # Should match with MIT
            {
                "school": "Massachusetts Institute of Technology",
                "major": "Computer Science",
                "start_date": "2018",
                "end_date": "2022",
            },
            # Should match with HUST
            {
                "school": "Hanoi University of Science and Technology",
                "major": "Electronics Engineering",
                "start_date": "2019",
                "degree": "Bachelor",
            },
            # Should be new
            {
                "school": "Stanford University",
                "major": "AI",
                "start_date": "2021",
                "degree": "PhD",
            },
        ]
    }

    result_dict = await agent.process(
        json.dumps(existing_cv, ensure_ascii=False),
        json.dumps(new_data, ensure_ascii=False),
    )

    print(f"Total actions: {result_dict['action_summary']['total_actions']}")
    print(f"Added: {result_dict['action_summary']['added']}")
    print(f"Updated: {result_dict['action_summary']['updated']}")
    print(f"Skipped: {result_dict['action_summary']['skipped']}")

    print("\nDetailed actions:")
    for i, action in enumerate(result_dict["detailed_actions"]):
        print(
            f"{i + 1}. {action['action']}: {action['item'].get('school', 'N/A')} - {action['reason']}"
        )

    # Should have 3 updates (for semantic matches) and 1 add (for Stanford)
    expected_updates = 3
    expected_adds = 1

    actual_updates = result_dict["action_summary"]["updated"]
    actual_adds = result_dict["action_summary"]["added"]

    print(f"\n✅ Expected {expected_updates} updates, got {actual_updates}")
    print(f"✅ Expected {expected_adds} adds, got {actual_adds}")

    return actual_updates >= 2 and actual_adds >= 1  # Allow some flexibility


async def test_company_semantic_matching():
    """Test semantic matching for companies"""
    print("\n=== Testing Company Semantic Matching ===")

    agent = CVDuplicateCheckerAgent()

    existing_cv = {
        "experience": [
            {
                "title": "Software Engineer",
                "company": "Google",
                "start_date": "2022-01-01",
            },
            {
                "title": "ML Engineer",
                "company": "Microsoft",
                "start_date": "2021-06-01",
            },
        ]
    }

    new_data = {
        "experience": [
            # Should match with Google
            {
                "title": "Software Engineer",
                "company": "Google LLC",
                "start_date": "2022-01-01",
                "end_date": "2023-12-31",
                "skills": ["Python", "Go"],
            },
            # Should match with Microsoft
            {
                "title": "ML Engineer",
                "company": "Microsoft Corporation",
                "start_date": "2021-06-01",
                "description": "Worked on Azure ML",
            },
            # Should be new
            {"title": "Data Scientist", "company": "Meta", "start_date": "2024-01-01"},
        ]
    }

    result_dict = await agent.process(json.dumps(existing_cv), json.dumps(new_data))

    print(f"Total actions: {result_dict['action_summary']['total_actions']}")
    print(f"Added: {result_dict['action_summary']['added']}")
    print(f"Updated: {result_dict['action_summary']['updated']}")

    print("\nDetailed actions:")
    for i, action in enumerate(result_dict["detailed_actions"]):
        print(
            f"{i + 1}. {action['action']}: {action['item'].get('company', 'N/A')} - {action['reason']}"
        )

    return (
        result_dict["action_summary"]["updated"] >= 1
        and result_dict["action_summary"]["added"] >= 1
    )


async def test_certification_semantic_matching():
    """Test semantic matching for certifications"""
    print("\n=== Testing Certification Semantic Matching ===")

    agent = CVDuplicateCheckerAgent()

    existing_cv = {
        "certification": [
            {"certification_name": "AWS Developer", "organization": "Amazon"},
            {"certification_name": "PMP", "organization": "PMI"},
        ]
    }

    new_data = {
        "certification": [
            # Should match with AWS Developer
            {
                "certification_name": "AWS Certified Developer Associate",
                "organization": "Amazon",
                "issue_date": "2023-06-01",
                "expire_date": "2026-06-01",
            },
            # Should match with PMP
            {
                "certification_name": "Project Management Professional",
                "organization": "Project Management Institute",
                "issue_date": "2022-12-01",
            },
            # Should be new
            {
                "certification_name": "CISSP",
                "organization": "ISC2",
                "issue_date": "2023-01-01",
            },
        ]
    }

    result_dict = await agent.process(json.dumps(existing_cv), json.dumps(new_data))

    print(f"Total actions: {result_dict['action_summary']['total_actions']}")
    print(f"Added: {result_dict['action_summary']['added']}")
    print(f"Updated: {result_dict['action_summary']['updated']}")

    print("\nDetailed actions:")
    for i, action in enumerate(result_dict["detailed_actions"]):
        cert_name = action["item"].get("certification_name", "N/A")
        print(f"{i + 1}. {action['action']}: {cert_name} - {action['reason']}")

    return (
        result_dict["action_summary"]["updated"] >= 1
        and result_dict["action_summary"]["added"] >= 1
    )


async def test_major_semantic_matching():
    """Test semantic matching for academic majors"""
    print("\n=== Testing Major/Field Semantic Matching ===")

    agent = CVDuplicateCheckerAgent()

    existing_cv = {
        "education": [
            {
                "school": "University A",
                "major": "Computer Science",
                "start_date": "2020",
            },
            {
                "school": "University B",
                "major": "Business Administration",
                "start_date": "2018",
            },
        ]
    }

    new_data = {
        "education": [
            # Should match with Computer Science
            {
                "school": "University A",
                "major": "Khoa học máy tính",
                "start_date": "2020",
                "degree": "Bachelor",
            },
            # Should match with Business Administration
            {
                "school": "University B",
                "major": "Quản trị kinh doanh",
                "start_date": "2018",
                "gpa": 3.7,
            },
            # Should be new
            {"school": "University C", "major": "Data Science", "start_date": "2022"},
        ]
    }

    result_dict = await agent.process(
        json.dumps(existing_cv, ensure_ascii=False),
        json.dumps(new_data, ensure_ascii=False),
    )

    print(f"Total actions: {result_dict['action_summary']['total_actions']}")
    print(f"Added: {result_dict['action_summary']['added']}")
    print(f"Updated: {result_dict['action_summary']['updated']}")

    print("\nDetailed actions:")
    for i, action in enumerate(result_dict["detailed_actions"]):
        major = action["item"].get("major", "N/A")
        print(f"{i + 1}. {action['action']}: {major} - {action['reason']}")

    return (
        result_dict["action_summary"]["updated"] >= 1
        and result_dict["action_summary"]["added"] >= 1
    )


async def test_edge_cases():
    """Test edge cases and fallback behavior"""
    print("\n=== Testing Edge Cases ===")

    agent = CVDuplicateCheckerAgent()

    # Test direct semantic comparison
    print("Testing direct semantic comparison:")

    test_cases = [
        ("DHBK", "Đại học Bách Khoa", "school", True),
        ("MIT", "Massachusetts Institute of Technology", "school", True),
        ("Google", "Google LLC", "company", True),
        ("Microsoft", "Microsoft Corporation", "company", True),
        ("AWS Developer", "AWS Certified Developer Associate", "certification", True),
        ("Computer Science", "Khoa học máy tính", "major", True),
        ("Random Text", "Completely Different", "general", False),
    ]

    correct_predictions = 0
    total_tests = len(test_cases)

    for value1, value2, field_type, expected in test_cases:
        try:
            result = await agent.test_semantic_similarity(value1, value2, field_type)
            print(
                f"  {value1} ≈ {value2} ({field_type}): {result} (expected: {expected})"
            )
            if result == expected:
                correct_predictions += 1
        except Exception as e:
            print(f"  ERROR testing {value1} ≈ {value2}: {e}")

    accuracy = correct_predictions / total_tests
    print(
        f"\nSemantic matching accuracy: {accuracy:.2%} ({correct_predictions}/{total_tests})"
    )

    return accuracy >= 0.7  # Allow 70% accuracy threshold


async def main():
    """Run all semantic matching tests"""
    print("🔍 Testing LLM-based Semantic Matching for CV Duplicate Detection\n")

    tests = [
        ("School Semantic Matching", test_school_semantic_matching),
        ("Company Semantic Matching", test_company_semantic_matching),
        ("Certification Semantic Matching", test_certification_semantic_matching),
        ("Major/Field Semantic Matching", test_major_semantic_matching),
        ("Edge Cases and Direct Comparison", test_edge_cases),
    ]

    passed = 0
    failed = 0

    for test_name, test_func in tests:
        try:
            success = await test_func()
            if success:
                print(f"✅ {test_name} - PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} - FAILED (didn't meet expectations)")
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
            failed += 1
        print("-" * 60)

    print(f"\n{'=' * 60}")
    print("SEMANTIC MATCHING TESTS SUMMARY")
    print(f"Total: {len(tests)} | Passed: {passed} | Failed: {failed}")
    print(f"{'=' * 60}")

    if passed == len(tests):
        print("🎉 All semantic matching tests passed!")
    else:
        print(f"⚠️  {failed} test(s) failed. Check LLM responses and prompts.")


if __name__ == "__main__":
    asyncio.run(main())

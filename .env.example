OPENAI_API_KEY=""
API_HOST="0.0.0.0"
API_PORT="8080"
API_RELOAD="true"
API_LOG_LEVEL="debug"

# Redis Configuration
REDIS_ENABLED="true"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""
REDIS_DB="0"
REDIS_TTL="3600"
REDIS_MAX_CONNECTIONS="10"
REDIS_SOCKET_CONNECT_TIMEOUT="5"
REDIS_SOCKET_KEEPALIVE="true"
REDIS_RETRY_ON_TIMEOUT="true"
REDIS_HEALTH_CHECK_INTERVAL="30"
REDIS_KEY_PREFIX="cv-dup-checker"

# Sentry Configuration
SENTRY_DSN=""
SENTRY_ENABLED="false"
SENTRY_ENVIRONMENT="development"
SENTRY_SAMPLE_RATE="1.0"
SENTRY_TRACES_SAMPLE_RATE="0.1"

# Langfuse Configuration
LANGFUSE_PUBLIC_KEY=""
LANGFUSE_SECRET_KEY=""
LANGFUSE_HOST="https://cloud.langfuse.com"

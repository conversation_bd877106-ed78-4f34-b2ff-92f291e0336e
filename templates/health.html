{% extends "base.html" %}

{% block title %}CV Duplicate Checker - Health Status{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-heart-pulse"></i> System Health</h1>
            <button type="button" class="btn btn-outline-primary" onclick="refreshHealth()">
                <i class="bi bi-arrow-clockwise" id="refreshIcon"></i> Refresh
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- API Status -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-server"></i> API Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            {% if health_data.status == 'healthy' %}
                                <i class="bi bi-check-circle display-4 text-success"></i>
                                <h6 class="text-success mt-2">Healthy</h6>
                            {% else %}
                                <i class="bi bi-x-circle display-4 text-danger"></i>
                                <h6 class="text-danger mt-2">Unhealthy</h6>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-6">
                        <dl class="row mb-0">
                            <dt class="col-6">Status:</dt>
                            <dd class="col-6">
                                {% if health_data.status == 'healthy' %}
                                    <span class="badge bg-success">{{ health_data.status.title() }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ health_data.status.title() }}</span>
                                {% endif %}
                            </dd>
                            <dt class="col-6">Version:</dt>
                            <dd class="col-6">{{ health_data.version }}</dd>
                            <dt class="col-6">Agent:</dt>
                            <dd class="col-6">
                                {% if health_data.agent_ready %}
                                    <span class="badge bg-success">Ready</span>
                                {% else %}
                                    <span class="badge bg-danger">Not Ready</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Agent Status -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-robot"></i> CV Agent Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            {% if health_data.agent_ready %}
                                <i class="bi bi-cpu display-4 text-success"></i>
                                <h6 class="text-success mt-2">Ready</h6>
                            {% else %}
                                <i class="bi bi-cpu display-4 text-danger"></i>
                                <h6 class="text-danger mt-2">Not Ready</h6>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-6">
                        {% if health_data.agent_ready %}
                            <div class="alert alert-success py-2 px-3 mb-0">
                                <small>
                                    <i class="bi bi-check-circle"></i>
                                    Agent is initialized and ready to process CV duplicate detection requests.
                                </small>
                            </div>
                        {% else %}
                            <div class="alert alert-danger py-2 px-3 mb-0">
                                <small>
                                    <i class="bi bi-exclamation-triangle"></i>
                                    Agent is not available. Check OpenAI API key configuration.
                                </small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>API Endpoints</h6>
                        <ul class="list-unstyled mb-3">
                            <li>
                                <i class="bi bi-link-45deg text-primary"></i>
                                <code>GET /health</code> - Health check
                            </li>
                            <li>
                                <i class="bi bi-link-45deg text-primary"></i>
                                <code>POST /check-duplicates</code> - CV processing
                            </li>
                            <li>
                                <i class="bi bi-link-45deg text-primary"></i>
                                <code>GET /docs</code> - API documentation
                            </li>
                            <li>
                                <i class="bi bi-link-45deg text-primary"></i>
                                <code>GET /web</code> - Web interface
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Features</h6>
                        <ul class="list-unstyled mb-3">
                            <li>
                                <i class="bi bi-check text-success"></i>
                                Semantic matching (LLM-powered)
                            </li>
                            <li>
                                <i class="bi bi-check text-success"></i>
                                Parallel CV section processing
                            </li>
                            <li>
                                <i class="bi bi-check text-success"></i>
                                Multi-language support
                            </li>
                            <li>
                                <i class="bi bi-check text-success"></i>
                                10 CV sections supported
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Connection -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-play-circle"></i> Test Connection</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Test the API connection and agent functionality.</p>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" onclick="testHealthEndpoint()">
                        <i class="bi bi-heart-pulse"></i> Test Health Endpoint
                    </button>
                    <button type="button" class="btn btn-success" onclick="testDuplicateDetection()">
                        <i class="bi bi-gear"></i> Test Duplicate Detection
                    </button>
                </div>
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<!-- Error Information -->
{% if error %}
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-danger" role="alert">
            <h6 class="alert-heading"><i class="bi bi-exclamation-triangle"></i> Error Details</h6>
            <p class="mb-0">{{ error }}</p>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block scripts %}
<script>
async function refreshHealth() {
    const refreshIcon = document.getElementById('refreshIcon');
    refreshIcon.classList.add('fa-spin');

    try {
        const response = await fetch('/health');
        if (response.ok) {
            location.reload();
        } else {
            showTestResult('Failed to refresh health status', 'danger');
        }
    } catch (error) {
        showTestResult('Network error: ' + error.message, 'danger');
    } finally {
        refreshIcon.classList.remove('fa-spin');
    }
}

async function testHealthEndpoint() {
    showTestResult('Testing health endpoint...', 'info');

    try {
        const response = await fetch('/health');
        const data = await response.json();

        if (response.ok) {
            showTestResult(`✅ Health endpoint working. Status: ${data.status}, Agent Ready: ${data.agent_ready}`, 'success');
        } else {
            showTestResult(`❌ Health endpoint error: ${response.status}`, 'danger');
        }
    } catch (error) {
        showTestResult(`❌ Network error: ${error.message}`, 'danger');
    }
}

async function testDuplicateDetection() {
    showTestResult('Testing duplicate detection...', 'info');

    const testData = {
        existing_cv: {
            education: [{ school: "Test University", major: "Computer Science", start_date: "2020" }],
            experience: [],
            extra_curricular: [],
            certification: [],
            language: [],
            achievement: [],
            reference: [],
            external_doc: [],
            summary: [],
            hobby: []
        },
        new_data: {
            education: [{ school: "Test University", major: "Computer Science", start_date: "2020", degree: "Bachelor" }],
            experience: [],
            extra_curricular: [],
            certification: [],
            language: [],
            achievement: [],
            reference: [],
            external_doc: [],
            summary: [],
            hobby: []
        }
    };

    try {
        const response = await fetch('/check-duplicates', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        const data = await response.json();

        if (response.ok) {
            showTestResult(`✅ Duplicate detection working. Total actions: ${data.action_summary.total_actions}`, 'success');
        } else {
            showTestResult(`❌ Duplicate detection error: ${data.message || 'Unknown error'}`, 'danger');
        }
    } catch (error) {
        showTestResult(`❌ Network error: ${error.message}`, 'danger');
    }
}

function showTestResult(message, type) {
    const resultsDiv = document.getElementById('testResults');
    resultsDiv.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}
</script>
{% endblock %}

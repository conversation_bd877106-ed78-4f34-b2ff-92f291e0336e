#!/usr/bin/env python3
"""
Load Testing for Memory Leak Detection

Simulates high-load scenarios to detect memory leaks in CV processing.
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import List

import matplotlib.pyplot as plt
import psutil

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from cv_duplicate_checker import CVDuplicateCheckerAgent


class MemoryLoadTester:
    """Load testing with memory monitoring"""

    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.memory_samples = []
        self.timestamps = []
        self.agent = None

    def get_memory_mb(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024

    def record_memory(self, label: str = ""):
        """Record current memory usage with timestamp"""
        memory = self.get_memory_mb()
        timestamp = datetime.now()

        self.memory_samples.append(memory)
        self.timestamps.append(timestamp)

        print(f"📊 {timestamp.strftime('%H:%M:%S')} - {label}: {memory:.2f} MB")

    async def run_cv_processing_batch(self, batch_size: int = 10) -> List[str]:
        """Run a batch of CV processing operations"""
        results = []

        # Sample CV data variations
        cv_variations = self.generate_cv_variations(batch_size)

        print(f"🔄 Processing batch of {batch_size} CVs...")

        tasks = []
        for i, (existing_cv, new_data) in enumerate(cv_variations):
            task = self.agent.process(
                json.dumps(existing_cv, ensure_ascii=False),
                json.dumps(new_data, ensure_ascii=False),
            )
            tasks.append(task)

        # Process all CVs concurrently
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                print(f"❌ CV {i + 1} failed: {result}")
            else:
                results.append(result)

        return results

    def generate_cv_variations(self, count: int) -> List[tuple]:
        """Generate varied CV data for testing"""
        variations = []

        base_existing = {
            "education": [
                {
                    "school": "University A",
                    "major": "Computer Science",
                    "start_date": "2018",
                }
            ],
            "experience": [
                {
                    "title": "Developer",
                    "company": "Tech Corp",
                    "start_date": "2022-01-01",
                }
            ],
            "certification": [],
            "language": [{"language_name": "English"}],
            "extra_curricular": [],
            "achievement": [],
            "reference": [],
            "external_doc": [],
            "summary": [],
            "hobby": [],
        }

        for i in range(count):
            # Create variations by adding more data
            existing_cv = base_existing.copy()
            new_data = {
                "education": [
                    {
                        "school": f"University {chr(65 + i % 26)}",
                        "major": "Computer Science",
                        "start_date": "2018",
                        "end_date": "2022",
                        "degree": "Bachelor",
                        "gpa": 3.5 + (i % 10) / 10,
                    }
                ],
                "experience": [
                    {
                        "title": "Developer",
                        "company": "Tech Corp",
                        "start_date": "2022-01-01",
                        "end_date": "2023-12-31",
                        "skills": [f"Skill_{j}" for j in range(i % 5 + 1)],
                    },
                    {
                        "title": f"Role_{i}",
                        "company": f"Company_{i}",
                        "start_date": "2024-01-01",
                        "description": f"Description for role {i}" * (i % 3 + 1),
                    },
                ],
                "certification": [
                    {
                        "certification_name": f"Cert_{i}",
                        "organization": f"Org_{i}",
                        "issue_date": "2023-01-01",
                    }
                ],
                "language": [
                    {"language_name": "English", "level": "Advanced"},
                    {"language_name": f"Language_{i}", "level": "Intermediate"},
                ],
                "extra_curricular": [],
                "achievement": [],
                "reference": [],
                "external_doc": [],
                "summary": [],
                "hobby": [],
            }

            variations.append((existing_cv, new_data))

        return variations

    async def stress_test_memory(
        self,
        iterations: int = 50,
        batch_size: int = 5,
        delay_between_batches: float = 1.0,
    ):
        """Run stress test to detect memory leaks"""
        print("🧪 Starting memory stress test:")
        print(f"   - {iterations} iterations")
        print(f"   - {batch_size} CVs per batch")
        print(f"   - {delay_between_batches}s delay between batches")
        print(f"   - Total CVs: {iterations * batch_size}")

        # Initialize agent
        print("\n🤖 Initializing agent...")
        self.record_memory("Initial")
        self.agent = CVDuplicateCheckerAgent()
        self.record_memory("Agent Initialized")

        # Run stress test
        for iteration in range(iterations):
            print(f"\n🔄 Iteration {iteration + 1}/{iterations}")

            # Process batch
            await self.run_cv_processing_batch(batch_size)
            self.record_memory(f"Batch {iteration + 1} Complete")

            # Check for memory growth
            if len(self.memory_samples) > 1:
                growth = self.memory_samples[-1] - self.memory_samples[-2]
                if growth > 10:  # More than 10MB growth
                    print(f"⚠️  High memory growth detected: +{growth:.2f} MB")

            # Delay between batches
            if delay_between_batches > 0:
                await asyncio.sleep(delay_between_batches)

        # Final memory check
        self.record_memory("Test Complete")

        # Analyze results
        self.analyze_memory_trend()
        self.plot_memory_usage()

    def analyze_memory_trend(self):
        """Analyze memory usage trend for leaks"""
        print("\n📈 Memory Trend Analysis:")

        if len(self.memory_samples) < 2:
            print("❌ Not enough data points for analysis")
            return

        start_memory = self.memory_samples[0]
        end_memory = self.memory_samples[-1]
        peak_memory = max(self.memory_samples)

        total_growth = end_memory - start_memory
        peak_growth = peak_memory - start_memory

        print(f"   Start Memory: {start_memory:.2f} MB")
        print(f"   End Memory: {end_memory:.2f} MB")
        print(f"   Peak Memory: {peak_memory:.2f} MB")
        print(f"   Total Growth: {total_growth:.2f} MB")
        print(f"   Peak Growth: {peak_growth:.2f} MB")

        # Detect potential leaks
        if total_growth > 50:  # More than 50MB growth
            print("🚨 POTENTIAL MEMORY LEAK DETECTED!")
            print(f"   Memory grew by {total_growth:.2f} MB during test")
        elif total_growth > 20:
            print("⚠️  Moderate memory growth detected")
            print("   Consider investigating further")
        else:
            print("✅ Memory usage appears stable")

    def plot_memory_usage(self, save_path: str = "memory_usage_plot.png"):
        """Generate memory usage plot"""
        try:
            plt.figure(figsize=(12, 6))
            plt.plot(
                range(len(self.memory_samples)), self.memory_samples, "b-", linewidth=2
            )
            plt.title("Memory Usage During Load Test")
            plt.xlabel("Sample Number")
            plt.ylabel("Memory Usage (MB)")
            plt.grid(True, alpha=0.3)

            # Add trend line
            if len(self.memory_samples) > 1:
                x = range(len(self.memory_samples))
                z = np.polyfit(x, self.memory_samples, 1)
                p = np.poly1d(z)
                plt.plot(
                    x,
                    p(x),
                    "r--",
                    alpha=0.8,
                    label=f"Trend (slope: {z[0]:.3f} MB/sample)",
                )
                plt.legend()

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"📊 Memory usage plot saved to {save_path}")

        except ImportError:
            print("⚠️  matplotlib not available, skipping plot generation")
        except Exception as e:
            print(f"❌ Error generating plot: {e}")

    def save_memory_data(self, filename: str = "memory_data.json"):
        """Save memory data for further analysis"""
        data = {
            "timestamps": [ts.isoformat() for ts in self.timestamps],
            "memory_samples": self.memory_samples,
            "summary": {
                "start_memory": self.memory_samples[0] if self.memory_samples else 0,
                "end_memory": self.memory_samples[-1] if self.memory_samples else 0,
                "peak_memory": max(self.memory_samples) if self.memory_samples else 0,
                "sample_count": len(self.memory_samples),
            },
        }

        with open(filename, "w") as f:
            json.dump(data, f, indent=2)

        print(f"💾 Memory data saved to {filename}")


async def main():
    """Run memory leak load test"""
    tester = MemoryLoadTester()

    # Run stress test
    await tester.stress_test_memory(
        iterations=20,  # 20 iterations
        batch_size=3,  # 3 CVs per batch
        delay_between_batches=0.5,  # 0.5s delay
    )

    # Save data for analysis
    tester.save_memory_data()


if __name__ == "__main__":
    try:
        import numpy as np
    except ImportError:
        print("⚠️  numpy not available, trend analysis will be limited")

    asyncio.run(main())

#!/usr/bin/env python3
"""
Memory Leak Detection Tools for CV Duplicate Checker

This script provides various memory profiling and leak detection capabilities.
"""

import gc
import os
import sys
import psutil
import tracemalloc
from pympler import tracker, muppy, summary
import asyncio
import time


class MemoryProfiler:
    """Comprehensive memory profiling and leak detection"""

    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.memory_tracker = tracker.SummaryTracker()
        self.baseline_memory = None
        self.snapshots = []

    def start_profiling(self):
        """Start memory profiling session"""
        tracemalloc.start()
        self.baseline_memory = self.get_memory_usage()
        print(f"🔍 Memory profiling started. Baseline: {self.baseline_memory:.2f} MB")

    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024

    def take_snapshot(self, label: str = None):
        """Take memory snapshot for comparison"""
        if not tracemalloc.is_tracing():
            tracemalloc.start()

        snapshot = tracemalloc.take_snapshot()
        current_memory = self.get_memory_usage()

        self.snapshots.append(
            {
                "label": label or f"snapshot_{len(self.snapshots)}",
                "snapshot": snapshot,
                "memory_mb": current_memory,
                "timestamp": time.time(),
            }
        )

        print(f"📸 Snapshot taken: {label} - Memory: {current_memory:.2f} MB")

    def compare_snapshots(self, start_idx: int = 0, end_idx: int = -1):
        """Compare memory snapshots to detect leaks"""
        if len(self.snapshots) < 2:
            print("❌ Need at least 2 snapshots for comparison")
            return

        start_snap = self.snapshots[start_idx]
        end_snap = self.snapshots[end_idx]

        print(f"\n🔍 Memory Comparison: {start_snap['label']} → {end_snap['label']}")
        print(
            f"Memory Change: {end_snap['memory_mb'] - start_snap['memory_mb']:.2f} MB"
        )

        # Compare tracemalloc snapshots
        top_stats = end_snap["snapshot"].compare_to(start_snap["snapshot"], "lineno")

        print("\n📊 Top 10 Memory Changes:")
        for index, stat in enumerate(top_stats[:10], 1):
            print(f"{index:2d}. {stat}")

    def detect_growing_objects(self):
        """Detect objects that are growing in memory"""
        print("\n🔍 Analyzing Object Growth...")

        # Get all objects in memory
        all_objects = muppy.get_objects()
        object_summary = summary.summarize(all_objects)

        print("📊 Top Object Types by Count:")
        for row in object_summary[:15]:
            print(f"  {row[0]:>8} {row[1]:<30} {row[2]:>10} bytes")

    def check_circular_references(self):
        """Check for circular references that prevent GC"""
        print("\n🔍 Checking Circular References...")

        # Get objects that can't be collected
        uncollectable = gc.garbage
        if uncollectable:
            print(f"⚠️  Found {len(uncollectable)} uncollectable objects:")
            for obj in uncollectable[:10]:  # Show first 10
                print(f"  - {type(obj)}: {repr(obj)[:100]}")
        else:
            print("✅ No uncollectable objects found")

        # Manual garbage collection
        collected = gc.collect()
        print(f"🗑️  Collected {collected} objects via garbage collection")

    def profile_cv_processing(self, agent, existing_cv: str, new_data: str):
        """Profile memory usage during CV processing"""
        print("\n🧪 Profiling CV Processing...")

        self.take_snapshot("before_processing")

        # Run CV processing
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(agent.process(existing_cv, new_data))
            self.take_snapshot("after_processing")
        finally:
            loop.close()

        # Force garbage collection
        gc.collect()
        self.take_snapshot("after_gc")

        # Compare memory usage
        self.compare_snapshots(-3, -1)  # Compare before to after GC

        return result

    def monitor_api_requests(self, num_requests: int = 10):
        """Monitor memory during multiple API requests"""
        print(f"\n🔄 Monitoring memory during {num_requests} API requests...")

        # This would need to be integrated with actual API calls
        # For now, it's a placeholder for the monitoring structure
        pass

    def generate_report(self, output_file: str = "memory_report.txt"):
        """Generate comprehensive memory leak report"""
        with open(output_file, "w") as f:
            f.write("# Memory Leak Detection Report\n\n")
            f.write(f"Current Memory Usage: {self.get_memory_usage():.2f} MB\n")
            f.write(f"Baseline Memory: {self.baseline_memory:.2f} MB\n")
            f.write(
                f"Memory Increase: {self.get_memory_usage() - (self.baseline_memory or 0):.2f} MB\n\n"
            )

            f.write(f"Total Snapshots Taken: {len(self.snapshots)}\n")
            for i, snap in enumerate(self.snapshots):
                f.write(f"  {i+1}. {snap['label']}: {snap['memory_mb']:.2f} MB\n")

        print(f"📄 Memory report saved to {output_file}")


def profile_agent_initialization():
    """Profile memory usage during agent initialization"""
    print("🔍 Profiling Agent Initialization...")

    profiler = MemoryProfiler()
    profiler.start_profiling()

    # Import and initialize agent
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))
    from cv_duplicate_checker import CVDuplicateCheckerAgent

    profiler.take_snapshot("before_agent_init")
    agent = CVDuplicateCheckerAgent()
    profiler.take_snapshot("after_agent_init")

    profiler.compare_snapshots(0, 1)
    profiler.detect_growing_objects()
    profiler.check_circular_references()

    return agent, profiler


if __name__ == "__main__":
    # Example usage
    agent, profiler = profile_agent_initialization()

    # Example CV data for testing
    existing_cv = '{"education": []}'
    new_data = '{"education": [{"school": "Test University", "major": "CS"}]}'

    # Profile CV processing
    result = profiler.profile_cv_processing(agent, existing_cv, new_data)

    # Generate report
    profiler.generate_report()

    print("\n✅ Memory profiling complete!")
